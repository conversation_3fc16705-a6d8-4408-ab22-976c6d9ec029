// REQUIRED VARIABLES (variables which are needed to be passed)
variable "faName" {
  description = "The name which should be used for this Windows Function App"
  type        = string
}
variable "rgName" {
  description = "The name of the Resource Group where the Windows Function App should exist"
  type        = string
}
variable "location" {
  description = "The Azure Region where the Windows Function App should exist"
  type        = string
}
variable "aspId" {
  description = "The ID of the App Service Plan within which to create the Function App"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "storageAccountName" {
  description = " The backend storage account name which will be used by the Function App"
  type        = string
}
variable "storageAccountaccesskey" {
  description = "The access key which will be used to access the backend storage account for the Function App"
  type        = string
}
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "virtualNetworkSubnetId" {
  description = "The subnet id which will be used by the Function App for regional virtual network integration"
  type        = string
}
variable "vnetRouteAllEnabled" {
  description = "Defines all outbound traffic to have NAT Gateways, Network Security Groups and User Defined Routes applied"
  type        = bool
}
variable "publicNetworkAccessEnabled" {
  description = "Defines whether public network access be enabled for the Function App or not"
  type        = bool
}
variable "alwaysOn" {
  description = "Defines if the Windows Web App is Always On enabled"
  type        = bool
}
variable "use32BitWorker" {
  description = "Defines the Windows Web App use a 32-bit worker process or not"
  type        = bool
}
variable "identityType" {
  description = "Specifies the type of Managed Service Identity that should be configured"
  type        = string
}
variable "dotnetStack" {
  description = "The version of .NET to use"
  type = object({
    dotnetVersion = string
  })
}
variable "elasticInstanceMinimum" {
  description = " The number of minimum instances for the Windows Function App"
  type        = number
}