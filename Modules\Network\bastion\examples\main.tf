// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "bastion" {
  source    = "../" // Modify module path accordingly 
  location  = "Central India"
  rgName    = "Demo-RG"
  
  bastionSettings = {
    bastionName         = "Demo-Bastion"
    sku                 = "Standard"
    ipConfigurationName = "configuration"
    scaleUnits        = 2
  }
  
  bastionConfig = {
    copyPasteEnabled = true
    fileCopyEnabled  = true
    tunnelingEnabled = true
  }
  
  subnetId          = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/virtualNetworks/demo-vnet/subnets/AzureBastionSubnet"
  publicIpAddressId = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/publicIPAddresses/demo-pip"
  
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}