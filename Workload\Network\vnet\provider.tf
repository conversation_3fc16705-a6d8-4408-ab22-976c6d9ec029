terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "4.1.0"
    }
  }
  backend "azurerm" {
    storage_account_name = "tfprojstate"
    container_name       = "tfstate"
    key                  = "terraform.tfstate"
    subscription_id = "d833250a-cb43-419a-bf49-27b8942a3335"
    access_key = "****************************************************************************************"
  }
}

provider "azurerm" {
  features {
    key_vault {
      purge_soft_delete_on_destroy = true
    }
    resource_group {
      prevent_deletion_if_contains_resources = true
    }
  }
  # Configuration options
  subscription_id = "d833250a-cb43-419a-bf49-27b8942a3335"
}