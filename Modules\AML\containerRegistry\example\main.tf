// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "acr" {
  source                     = "../" // Modifiy module path accordingly 
  acrName                    = "ContainerRegistry"
  rgName                     = "Demo-RG"
  location                   = "Central India"
  acrSku                     = "Premium"
  publicNetworkAccessEnabled = false
  networkRuleBypassOption    = "AzureServices"
  zoneRedundancyEnabled      = false
  defaultAction              = "Allow"
  acrTags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}