// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "windowsVirtualMachine" {
  source       = "../" // Modifiy module path accordingly 
  publicIpName = "Demo-PublicIp"
  location     = "Central India"
  rgName       = "Demo-RG"
  publicIpSku  = "Standard"
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  nicName      = "DemoNic"
  subnetId     = "" // Associate Subnet Id on this place with the help of either Module (using Output), Locals or Resource Block
  vmName       = "DemoVM"
  computerName = "DemoVMName"
  osDiskName   = "DemoOsDisk"
  vmConfig = {
    vmSize                        = "Standard_DS2_v2"
    availabilityZone              = "1"
    adminUsername                 = "adminUser"
    adminPassword                 = "India@1947"
    caching                       = "ReadWrite"
    storageAccountType            = "Standard_LRS"
    availabilityZone              = "1"
    publisher                     = "MicrosoftWindowsServer"
    offer                         = "WindowsServer"
    sku                           = "2019-Datacenter"
    version                       = "latest"
    nicIpConfigurationName            = "Demo-nic"
    nicPrivateIpAddressAllocation = "Dynamic"
    publicIpAllocationMethod = "Dynamic"
  }
}