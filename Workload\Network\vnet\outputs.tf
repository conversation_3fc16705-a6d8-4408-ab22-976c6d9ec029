# Virtual Network Outputs
output "virtual_network_ids" {
  description = "Map of virtual network names to their IDs"
  value = {
    for k, v in module.virtual_networks : k => v.virtualNetworkId
  }
}

output "virtual_network_names" {
  description = "Map of virtual network keys to their names"
  value = {
    for k, v in module.virtual_networks : k => v.virtualNetworkName
  }
}

# Subnet Outputs
output "subnet_ids" {
  description = "Map of subnet names to their IDs"
  value = {
    for k, v in module.subnets : k => v.subnetId
  }
}

output "subnet_names" {
  description = "Map of subnet keys to their names"
  value = {
    for k, v in module.subnets : k => v.subnetName
  }
}

# NSG Outputs
output "nsg_ids" {
  description = "Map of NSG names to their IDs"
  value = {
    for k, v in module.network_security_groups : k => v.nsg_id
  }
}

output "nsg_names" {
  description = "Map of NSG keys to their names"
  value = {
    for k, v in module.network_security_groups : k => v.nsg_name
  }
}

# Route Table Outputs
output "route_table_ids" {
  description = "Map of route table names to their IDs"
  value = {
    for k, v in module.route_tables : k => v.routeTableId
  }
}

output "route_table_names" {
  description = "Map of route table keys to their names"
  value = {
    for k, v in module.route_tables : k => v.routeTableName
  }
}

# Public IP Outputs
output "public_ip_ids" {
  description = "Map of public IP names to their IDs"
  value = {
    for k, v in module.public_ips : k => v.publicIpId
  }
}

# Association Outputs
output "nsg_association_ids" {
  description = "Map of NSG association names to their IDs"
  value = {
    for k, v in module.nsg_associations : k => v.subnetNsgAssociationId
  }
}

output "route_table_association_ids" {
  description = "Map of route table association names to their IDs"
  value = {
    for k, v in module.route_table_associations : k => v.subnetNsgAssociationId
  }
}

# Hub and Spoke specific outputs for easy reference
output "hub_vnet_id" {
  description = "Hub VNet ID"
  value       = module.virtual_networks["hub_vnet"].virtualNetworkId
}

output "spoke_vnet_id" {
  description = "Spoke VNet ID"
  value       = module.virtual_networks["spoke_vnet"].virtualNetworkId
}

output "bastion_subnet_id" {
  description = "Bastion subnet ID"
  value       = module.subnets["hub_bastion_subnet"].subnetId
}

output "firewall_subnet_id" {
  description = "Firewall subnet ID"
  value       = module.subnets["hub_firewall_subnet"].subnetId
}

output "bastion_public_ip_id" {
  description = "Bastion public IP ID"
  value       = module.public_ips["bastion_pip"].publicIpId
}

output "firewall_public_ip_id" {
  description = "Firewall public IP ID"
  value       = module.public_ips["firewall_pip"].publicIpId
}
