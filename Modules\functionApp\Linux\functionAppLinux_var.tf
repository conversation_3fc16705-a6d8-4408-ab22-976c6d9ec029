// REQUIRED VARIABLES (variables which are needed to be passed)
variable "faName" {
  description = "The name which should be used for this Linux Function App"
  type        = string
}
variable "rgName" {
  description = "The name of the Resource Group where the Linux Function App should exist"
  type        = string
}
variable "location" {
  description = "The Azure Region where the Linux Function App should exist"
  type        = string
}
variable "aspId" {
  description = "The ID of the App Service Plan within which to create the Function App"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "storageAccountName" {
  description = " The backend storage account name which will be used by the Function App"
  type        = string
}
variable "storageAccountaccesskey" {
  description = "The access key which will be used to access the backend storage account for the Function App"
  type        = string
}
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "virtualNetworkSubnetId" {
  description = "The subnet id which will be used by the Function App for regional virtual network integration"
  type        = string
}
variable "vnetRouteAllEnabled" {
  description = "Defines all outbound traffic to have NAT Gateways, Network Security Groups and User Defined Routes applied"
  type        = bool
}
variable "publicNetworkAccessEnabled" {
  description = "Defines whether public network access be enabled for the Function App or not"
  type        = bool
}
variable "alwaysOn" {
  description = "Defines if the Linux Web App is Always On enabled"
  type        = bool
}
variable "use32BitWorker" {
  description = "Defines the Linux Web App use a 32-bit worker process or not"
  type        = bool
}
variable "identityType" {
  description = "Specifies the type of Managed Service Identity that should be configured"
  type        = string
}
variable "dockerStack" {
  description = "Details regarding Docker Configuration"
  type = object({
    registryUrl = string
    imageName   = string
    imageTag    = string
  })
}
variable "elasticInstanceMinimum" {
  description = " The number of minimum instances for the Linux Function App"
  type        = number
}
# variable "appinsightInstrumentationkey" {
#   description = "Specify the source of the function app code"
#   type        = string
# }
# variable "storageShareName" {
#   description = "The name of the Container which should be created within the Storage Account"
#   type        = string
# }
# variable "websiteContentovervnet" {
#   description = "Specify the source of the function app code"
#   type        = number
#   default     = 1
# }
# variable "websiteVnetRouteAll" {
#   description = "Specify the source of the function app code"
#   type        = number
#   default     = 1
# }
# variable "functionsWorkerRuntime" {
#   description = "Specify the runtime or programming language"
#   type        = string
# }
# variable "stackVersion" {
# description = "The version of NET to use Possible values"  
# type = list(object({
#     dotnet_version = string
#     python_version = string
#   })) 
# }
# variable "websiteRunFromPackage" {
#   description = "Specify the source of the function app code"
#   type        = number
# }
# variable "functionExtensionVersion" {
#   description = "The runtime version associated with the Function App"
#   type    = string
# }
# variable "storageConnectionString" {
#   description = "Specify the source of the function app code"
#   type        = string
# }
# variable "WebsiteUsePlaceholderDotnetIsolated" {
#   type    = string
#   default = "1"
# }
# variable "pythonStackVersion" {
#   type = string
# }
# variable "dotnetStack" {
#   description = "The version of .NET to use"
#   type = object({
#     dotnetVersion = string
#   })
# }