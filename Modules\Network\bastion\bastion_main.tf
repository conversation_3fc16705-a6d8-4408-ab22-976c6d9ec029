resource "azurerm_bastion_host" "bastion" {
  name                = var.bastionSettings.bastionName
  location            = var.location
  resource_group_name = var.rgName
  sku                 = var.bastionSettings.sku
  copy_paste_enabled  = var.bastionConfig.copyPasteEnabled
  file_copy_enabled   = var.bastionConfig.fileCopyEnabled
  tunneling_enabled   = var.bastionConfig.tunnelingEnabled
  scale_units         = var.bastionSettings.scaleUnits
  tags                = var.tags

  ip_configuration {
    name                 = var.bastionSettings.ipConfigurationName
    subnet_id            = var.subnetId
    public_ip_address_id = var.publicIpAddressId
  }
}