// REQUIRED VARIABLES (variables which are needed to be passed)
variable "aiSearchName" {
  description = "The Name given to this Search Service"
  type        = string
}
variable "aiSearchRgName" {
  description = "The name of the Resource Group where the Search Service should exist"
  type        = string
}
variable "aiSearchRgLocation" {
  description = "The Azure Region where the Search Service should exist"
  type        = string
}
variable "aiSearchSku" {
  description = "The SKU which should be used for this Search Service"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "aiSearchTag" {
  description = "Specifies a mapping of tags which should be assigned to this Search Service"
  type        = map(string)
}
variable "publicNetworkAccessEnabled" {
  description = "Defines Public Network Access. This variable accepts a bool value for ai search service"
  type        = bool
}
variable "aiServiceHostingMode" {
  description = "ai search service hosting mode. This variable accepts string for hosting mode"
  type        = string
}
variable "aiPartitionCount" {
  description = "ai partition count for ai search service accepts number"
  type        = number
}
variable "aiReplicaCount" {
  description = "ai replica count for ai search service accepts number"
  type        = number
}