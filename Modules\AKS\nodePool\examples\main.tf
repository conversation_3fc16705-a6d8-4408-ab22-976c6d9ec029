module "nodePools" {
  source                          = "../"
  kubernetes_cluster_id = "Pass_Kubernetes_Cluster_ID" // Associate Kubernetes Cluster ID on this Place with the help of either Module, Locals or Resource Block
  vnetId                          = "Pass_VNet_ID" // Associate Virtual Network Id on this Place with the help of either Module, Locals or Resource Block
  nodePool = {
    userPool = {
      name                = "userpool"
      mode                = "User"
      vm_size             = "Standard_DS2_v2"
      os_type             = "Linux"
      os_sku              = "Ubuntu"
      os_disk_type        = "Managed"
      os_disk_size_gb     = 128
      enable_auto_scaling = true
      node_count = 3
      max_count           = 5
      min_count           = 1
      zones               = ["1", "2", "3"]
      max_pods            = 30
      node_labels = {
        "environment" = "production"
        "workload"    = "general"
      }
   }
  }
}