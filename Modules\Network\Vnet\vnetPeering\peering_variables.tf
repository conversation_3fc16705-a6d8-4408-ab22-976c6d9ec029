// REQUIRED VARIABLES (variables which are needed to be passed)
variable "peeringName" {
  description = "Name of the virtual network peering connection"
  type        = string
}

variable "resourceGroupName" {
  description = "Name of the resource group containing the source virtual network"
  type        = string
}

variable "sourceVirtualNetworkName" {
  description = "Name of the source virtual network to create the peering from"
  type        = string
}

variable "remoteVirtualNetworkId" {
  description = "Resource ID of the remote virtual network to peer with"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "peeringOptions" {
  description = "Configuration options for the virtual network peering"
  type = object({
    allowVirtualNetworkAccess = optional(bool, true)  # Controls if the VMs in the remote virtual network can access VMs in the local virtual network
    allowForwardedTraffic     = optional(bool, false) # Controls if forwarded traffic from VMs in the remote virtual network is allowed
    allowGatewayTransit       = optional(bool, false) # Controls if gateway links from the remote virtual network are allowed
    useRemoteGateways         = optional(bool, false) # Controls if remote gateways can be used on the local virtual network
  })
  default = {}
}