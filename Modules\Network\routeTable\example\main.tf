// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "routeTable" {
  source                     = "../" // Modifiy module path accordingly 
  rtName                     = "DemoRouteTable"
  location                   = "Central India"
  rgName                     = "Demo-RG"
  disableBgpRoutePropagation = false
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  rtRoutes = [
    {
      name               = "DemoRoute"
      addressPrefix      = "********/16"
      nextHopType        = "VirtualAppliance"
      nextHopInIpAddress = "********/16"
    }
  ]
}