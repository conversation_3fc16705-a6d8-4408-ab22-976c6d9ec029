resource "azurerm_kubernetes_cluster" "kubernetesCluster" {
  name                                = var.aksName
  location                            = var.location
  resource_group_name                 = var.rgName
  sku_tier                            = var.skuTier
  private_cluster_enabled             = var.privateClusterEnabled
  private_cluster_public_fqdn_enabled = var.privateClusterPublicFqdnEnabled
  dns_prefix                          = var.dnsPrefix
  node_resource_group                 = var.node_resource_group
  network_profile {
    network_plugin = var.networkPlugin
    network_policy = var.networkPlugin
    service_cidr   = var.serviceCidr
    dns_service_ip = var.dnsServiceIp
  }
  default_node_pool {
    name                 = var.nodePoolName
    vnet_subnet_id       = var.vnetId
    vm_size              = var.vmSize
    os_sku               = var.osSku
    os_disk_type         = var.osDiskType
    os_disk_size_gb      = var.osDiskSizeGb
    zones                = var.nodePoolZones
    node_count           = var.nodeCount
    auto_scaling_enabled = var.enableAutoScaling
    max_count            = var.systemMaxCount
    min_count            = var.systemMinCount
    max_pods             = var.systemMaxPods
  }
  identity {
    type = var.identityType
  }
  tags = var.aksTags
  lifecycle {
    ignore_changes = [
      default_node_pool, key_vault_secrets_provider
    ]
  }
}

# resource "azurerm_kubernetes_cluster_node_pool" "NodeCC" {
#   for_each              = var.nodePool
#   name                  = each.value.name
#   kubernetes_cluster_id = azurerm_kubernetes_cluster.kubernetesCluster.id
#   vnet_subnet_id        = var.vnetId
#   mode                  = each.value.mode
#   vm_size               = each.value.vm_size
#   os_type               = each.value.os_type
#   os_sku                = each.value.os_sku
#   os_disk_type          = each.value.os_disk_type
#   os_disk_size_gb       = each.value.os_disk_size_gb
#   auto_scaling_enabled  = each.value.enable_auto_scaling
#   node_count            = each.value.node_count
#   max_count   = each.value.max_count
#   min_count   = each.value.min_count
#   zones       = each.value.zones
#   max_pods    = each.value.max_pods
#   node_labels = each.value.node_labels
#   depends_on  = [azurerm_kubernetes_cluster.kubernetesCluster]
#   lifecycle {
#     ignore_changes = [
#       max_count, min_count
#     ]
#   }
# }
