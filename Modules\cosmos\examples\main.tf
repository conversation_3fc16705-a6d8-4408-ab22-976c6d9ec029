// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "cosmosDb" {
  source   = "../" // Modify module path accordingly 
  location = "Central India"
  rgName   = "Demo-RG"
  
  cosmosSettings = {
    accountName = "demo-cosmos-account"
    kind        = "GlobalDocumentDB"
    offerType   = "Standard"
  }
  
  cosmosConfig = {
    enableAutomaticFailover     = true
    publicNetworkAccessEnabled = true
    enableFreeTier              = false
    analyticalStorageEnabled    = false
  }
  
  consistencyPolicy = {
    level = "Session"
  }
  
  geoLocations = [
    {
      location          = "Central India"
      failoverPriority = 0
      zoneRedundant    = false
    },
    {
      location          = "East US"
      failoverPriority = 1
      zoneRedundant    = false
    }
  ]
  
  capabilities = [
    "EnableServerless",
    "EnableAggregationPipeline"
  ]
  
  backupPolicy = {
    type               = "Periodic"
    intervalInMinutes = 240
    retentionInHours  = 8
  }
  
  sqlDatabases = {
    "demo-database" = {
      maxThroughput = 4000
    }
  }
  
  sqlContainers = {
    "container1" = {
      name              = "users"
      databaseName     = "demo-database"
      partitionKeyPath = "/id"
      maxThroughput    = 4000
      indexingPolicy   = {
        indexingMode  = "consistent"
        includedPaths = ["/name/*", "/address/*"]
        excludedPaths = ["/temp/*"]
      }
      uniqueKeys      = [
        ["/email"]
      ]
    },
    "container2" = {
      name              = "products"
      databaseName     = "demo-database"
      partitionKeyPath = "/category"
      maxThroughput    = 4000
    }
  }
  
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}