output "azureMLId" {
  description = "The ID of the Azure Machine Learning Workspace"
  value       = azurerm_machine_learning_workspace.azureML.id
}

output "azureMLName" {
  description = "The name of the Azure Machine Learning Workspace"
  value       = azurerm_machine_learning_workspace.azureML.name
}

output "azureMLIdentityPrincipalId" {
  description = "The Principal ID of the System Assigned Identity of the Azure Machine Learning Workspace"
  value       = azurerm_machine_learning_workspace.azureML.identity[0].principal_id
}

output "azureMLIdentityTenantId" {
  description = "The Tenant ID of the System Assigned Identity of the Azure Machine Learning Workspace"
  value       = azurerm_machine_learning_workspace.azureML.identity[0].tenant_id
}

output "azureMLDiscoveryUrl" {
  description = "The URL for the discovery service to identify regional endpoints for machine learning experimentation services"
  value       = azurerm_machine_learning_workspace.azureML.discovery_url
}