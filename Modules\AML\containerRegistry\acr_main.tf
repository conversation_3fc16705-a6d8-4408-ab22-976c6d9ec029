resource "azurerm_container_registry" "acr" {
  name                          = var.acrName
  resource_group_name           = var.rgName
  location                      = var.location
  sku                           = var.acrSku
  public_network_access_enabled = var.publicNetworkAccessEnabled
  network_rule_bypass_option    = var.networkRuleBypassOption
  zone_redundancy_enabled       = var.zoneRedundancyEnabled
  tags                          = var.acrTags
  network_rule_set {
    default_action = var.defaultAction
  }
  lifecycle {
    ignore_changes = []
  }
}
