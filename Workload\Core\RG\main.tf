# Azure Firewall
module "azure_firewalls" {
  for_each = var.azure_firewalls
  source   = "../../../Modules/Firewall/firewall"

  firewallName        = each.value.name
  location            = local.location
  rgName              = local.hub_rg_name
  skuName             = each.value.sku_name
  skuTier             = each.value.sku_tier
  zones               = each.value.zones
  tags                = merge(local.common_tags, each.value.tags)
  ipConfigurationName = each.value.ip_configuration_name
  subnetId            = data.azurerm_subnet.hub_firewall_subnet.id
  publicIpId          = data.azurerm_public_ip.firewall_pip.id
}

# Azure Bastion
module "azure_bastions" {
  for_each = var.azure_bastions
  source   = "../../../Modules/Network/bastion"

  rgName              = local.hub_rg_name
  location            = local.location
  bastionSettings = {
    bastionName         = each.value.name
    sku                 = each.value.bastion_settings.sku
    ipConfigurationName = each.value.bastion_settings.ip_configuration_name
    scaleUnits          = each.value.bastion_settings.scale_units
  }
  bastionConfig = {
    copyPasteEnabled = each.value.bastion_config.copy_paste_enabled
    fileCopyEnabled  = each.value.bastion_config.file_copy_enabled
    tunnelingEnabled = each.value.bastion_config.tunneling_enabled
  }
  subnetId          = data.azurerm_subnet.hub_bastion_subnet.id
  publicIpAddressId = data.azurerm_public_ip.bastion_pip.id
  tags              = merge(local.common_tags, each.value.tags)
}

# Public IP for Application Gateway
module "app_gateway_public_ips" {
  for_each = var.application_gateways
  source   = "../../../Modules/Network/publicIp"

  publicIpName     = "${each.value.name}-PublicIP"
  location         = local.location
  rgName           = local.hub_rg_name
  allocationMethod = "Static"
  publicIpSKU      = "Standard"
  zones            = ["1"]
  tags             = merge(local.common_tags, each.value.tags, { Purpose = "Application Gateway" })
}

# Application Gateway
module "application_gateways" {
  for_each = var.application_gateways
  source   = "../../../Modules/Network/applicationGateway"

  applicationGatewayName          = each.value.name
  rgName                          = local.hub_rg_name
  location                        = local.location
  sku                             = each.value.sku
  tier                            = each.value.tier
  capacity                        = each.value.capacity
  gatewayIpConfiguration          = each.value.gateway_ip_configuration
  subnetId                        = data.azurerm_subnet.hub_normal_subnet.id
  frontendPortName                = each.value.frontend_port_name
  port                            = each.value.port
  frontendIpConfigurationName     = each.value.frontend_ip_configuration_name
  publicIpId                      = module.app_gateway_public_ips[each.key].publicIpId
  backendAddressPoolName          = each.value.backend_address_pool_name
  httpSettingName                 = each.value.http_setting_name
  cookieBasedAffinity             = each.value.cookie_based_affinity
  path                            = each.value.path
  protocol                        = each.value.protocol
  requestTimeout                  = each.value.request_timeout
  listenerName                    = each.value.listener_name
  requestRoutingRuleName          = each.value.request_routing_rule_name
  ruleType                        = each.value.rule_type
  priority                        = each.value.priority
  apimTags                        = merge(local.common_tags, each.value.tags)

  depends_on = [module.app_gateway_public_ips]
}

# Private DNS Zones
module "private_dns_zones" {
  for_each = var.private_dns_zones
  source   = "../../../Modules/Network/privateDnsZone"

  privateDnsZoneName = each.value.name
  rgName             = local.hub_rg_name
  tags               = merge(local.common_tags, each.value.tags)
}

# Virtual Network Links for Private DNS Zones
module "vnet_links" {
  for_each = var.vnet_links
  source   = "../../../Modules/Network/Vnet/virtualNetworkLink"

  privateDnsLinkName                = each.value.name
  privateDnsLinkRegistrationEnabled = each.value.registration_enabled
  rgName                            = local.hub_rg_name
  privateDnsLinkZoneName            = module.private_dns_zones[each.value.dns_zone_key].privateDnsZoneName
  privateDnsLinkVirtualNetworkId    = local.hub_vnet_id
  tags                              = merge(local.common_tags, each.value.tags)

  depends_on = [module.private_dns_zones]
}