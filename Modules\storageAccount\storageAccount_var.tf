// REQUIRED VARIABLES (variables which are needed to be passed)
variable "storageAccountName" {
  description = "The name of the Azure Storage Account"
  type        = string
}
variable "accountTier" {
  description = "The storage account tier (Standard or Premium)"
  type        = string
}
variable "location" {
  description = "The region where the Storage Account will be created"
  type        = string
}
variable "rgName" {
  description = "The name of the resource group where the Storage Account will be created"
  type        = string
}
variable "accountReplicationType" {
  description = "The storage account replication type (LRS, GRS, RAGRS, ZRS, GZRS, or GZRS)"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "isHnsEnabled" {
  description = "Boolean flag to enable hierarchical namespace for the Storage Account"
  type        = bool
}
variable "publicNetworkAccessEnabled" {
  description = "Boolean flag to enable public network access for the Storage Account"
  type        = bool
}
variable "accountKind" {
  description = "Defines the kind of Storage Account"
  type        = string
}
variable "enableHttpsTrafficOnly" {
  description = "Boolean flag which forces HTTPS if enabled"
  type        = bool
}
variable "minTlsVersion" {
  description = "The minimum supported TLS version for the storage account"
  type        = string
}
variable "sharedAccessKeyEnabled" {
  description = "Indicates whether the storage account permits requests to be authorized with the account access key via Shared Key"
  type        = bool
}
variable "networkRules" {
  description = "Network rules configuration for the Storage Account"
  type = object({
    defaultAction = string
  })
}