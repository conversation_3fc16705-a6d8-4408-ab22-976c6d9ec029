# Virtual Networks
module "virtual_networks" {
  for_each = var.virtual_networks
  source   = "../../../Modules/Network/Vnet/virtualNetwork"

  virtualNetworkName         = each.value.name
  rgName                     = each.value.resource_group_name
  location                   = each.value.location
  virtualNetworkAddressSpace = each.value.address_space
  tags                       = each.value.tags
}

# Subnets
module "subnets" {
  for_each = var.subnets
  source   = "../../../Modules/Network/Vnet/subnet"

  subnetName                               = each.value.name
  rgName                                   = each.value.resource_group_name
  subnetAddressPrefixes                    = each.value.address_prefixes
  virtualNetworkName                       = module.virtual_networks[each.value.virtual_network_key].virtualNetworkName
  privateLinkServiceNetworkPoliciesEnabled = each.value.private_link_service_network_policies_enabled
  subnetDelegations                        = each.value.delegations
  subnetNsgAssociation                     = false
  subnetRtAssociation                      = false

  depends_on = [module.virtual_networks]
}

# Network Security Groups
module "network_security_groups" {
  for_each = var.network_security_groups
  source   = "../../../Modules/Network/networkSecurityGroup"

  nsgName  = each.value.name
  location = each.value.location
  rgName   = each.value.resource_group_name
  secRule  = each.value.security_rules
  tags     = each.value.tags
}

# Route Tables
module "route_tables" {
  for_each = var.route_tables
  source   = "../../../Modules/Network/routeTable"

  rtName                     = each.value.name
  location                   = each.value.location
  rgName                     = each.value.resource_group_name
  disableBgpRoutePropagation = each.value.disable_bgp_route_propagation
  rtRoutes                   = each.value.routes
  tags                       = each.value.tags
}

# NSG Associations
module "nsg_associations" {
  for_each = var.nsg_associations
  source   = "../../../Modules/Network/Vnet/association/NSG"

  nsgId    = module.network_security_groups[each.value.nsg_key].nsg_id
  subnetId = module.subnets[each.value.subnet_key].subnetId

  depends_on = [module.subnets, module.network_security_groups]
}

# Route Table Associations
module "route_table_associations" {
  for_each = var.route_table_associations
  source   = "../../../Modules/Network/Vnet/association/RT"

  rtId     = module.route_tables[each.value.route_table_key].routeTableId
  subnetId = module.subnets[each.value.subnet_key].subnetId

  depends_on = [module.subnets, module.route_tables]
}

# VNet Peering
module "vnet_peering" {
  for_each = var.vnet_peering
  source   = "../../../Modules/Network/Vnet/vnetPeering"

  peeringName              = each.value.name
  resourceGroupName        = each.value.resource_group_name
  sourceVirtualNetworkName = module.virtual_networks[each.value.source_vnet_key].virtualNetworkName
  remoteVirtualNetworkId   = module.virtual_networks[each.value.remote_vnet_key].virtualNetworkId
  peeringOptions           = each.value.peering_options

  depends_on = [module.virtual_networks]
}

# Public IPs
module "public_ips" {
  for_each = var.public_ips
  source   = "../../../Modules/Network/publicIp"

  publicIpName     = each.value.name
  location         = each.value.location
  rgName           = each.value.resource_group_name
  allocationMethod = each.value.allocation_method
  publicIpSKU      = each.value.sku
  zones            = each.value.zones
  tags             = each.value.tags
}