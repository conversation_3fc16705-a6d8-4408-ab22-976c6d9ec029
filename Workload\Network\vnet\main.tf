module "virtualNetwork1" {
  source                     = "../../../Modules/Network/Vnet/virtualNetwork" // Modifiy module path accordingly 
  virtualNetworkName         = "Demo-VNet1"
  rgName                     = "kartik.jindal_RG"
  location                   = "Central India"
  virtualNetworkAddressSpace = ["10.0.0.0/16"]
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}
module "virtualNetworkPeering" {
  source = "../../../Modules/Network/Vnet/vnetPeering" // Modify module path accordingly 

  // Required parameters as separate variables
  peeringName              = "vnet1-to-vnet2"
  resourceGroupName        = "kartik.jindal_RG"
  sourceVirtualNetworkName = "Demo-VNet1"
  remoteVirtualNetworkId   = module.virtualNetwork2.virtualNetworkId

  // Optional parameters as an object
  peeringOptions = {
    allowVirtualNetworkAccess = true
    allowForwardedTraffic     = false
    allowGatewayTransit       = false
    useRemoteGateways         = false
  }
  depends_on = [ module.virtualNetwork1 , module.virtualNetwork2 ]
}
module "subnets" {
  source                                   = "../../../Modules/Network/Vnet/subnet" // Modifiy module path accordingly 
  subnetName                               = "AzureBastionSubnet"
  rgName                                   = "kartik.jindal_RG"
  subnetAddressPrefixes                    = ["********/24"]
  virtualNetworkName                       = "Demo-Vnet1" // Associate Virtual Network Name on this Place with the help of either Module (using Output), Locals or Resource Block
  privateLinkServiceNetworkPoliciesEnabled = true
  subnetDelegations                        = null
  subnetNsgAssociation                     = false
  subnetRtAssociation                      = false
  depends_on                               = [module.virtualNetwork1]
}
module "nsg" {
  source   = "../../../Modules/Network/networkSecurityGroup" // Modifiy module path accordingly 
  nsgName  = "Demo-NSG"
  location = "Central India"
  rgName   = "kartik.jindal_RG"
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  secRule = [
    {
      name                     = "Demo-Rule"
      protocol                 = "Tcp"
      sourcePortRange          = "*"
      destinationPortRange     = "*"
      sourceAddressPrefix      = "*"
      destinationAddressPrefix = "*"
      access                   = "Allow"
      priority                 = 100
      direction                = "Inbound"
    }
  ]
}
module "nsgAssociation" {
  source   = "../../../Modules/Network/Vnet/association/NSG" // Modifiy module path accordingly 
  nsgId    = module.nsg.nsg_id
  subnetId = module.subnets.subnetId
  depends_on = [ module.subnets ]
}
module "routeTable" {
  source                     = "../../../Modules/Network/routeTable" // Modifiy module path accordingly 
  rtName                     = "DemoRouteTable"
  location                   = "Central India"
  rgName                     = "kartik.jindal_RG"
  disableBgpRoutePropagation = false
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  rtRoutes = [
    {
      name               = "DemoRoute"
      addressPrefix      = "********/16"
      nextHopType        = "VirtualAppliance"
      nextHopInIpAddress = "********"
    }
  ]
}
module "rtAssociation" {
  source   = "../../../Modules/Network/Vnet/association/RT" // Modifiy module path accordingly 
  rtId     = module.routeTable.routeTableId
  subnetId = module.subnets.subnetId
  depends_on = [ module.subnets ] 
}
module "pip" {
  source           = "../../../Modules/Network/publicIp" // Modifiy module path accordingly 
  publicIpName     = "Demo-IP"
  location         = "Central India"
  rgName           = "kartik.jindal_RG"
  allocationMethod = "Static"
  publicIpSKU      = "Standard"
  zones            = ["1"]
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}