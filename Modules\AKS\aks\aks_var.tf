// REQUIRED VARIABLES (variables which are needed to be passed)
variable "aksName" {
  description = "Name of the Managed Kubernetes Cluster"
  type        = string
}
variable "location" {
  description = "Location of the Managed Kubernetes Cluster"
  type        = string
}
variable "rgName" {
  description = "Specifies the Resource Group where the Managed Kubernetes Cluster should exist"
  type        = string
}
variable "nodePoolName" {
  description = "Name which should be used for the default Kubernetes Node Pool"
  type        = string
}
variable "vmSize" {
  description = "The size of the Virtual Machine inside the Default Pool"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "skuTier" {
  description = "SKU Tier that should be used for this Kubernetes Cluster"
  type        = string
}
variable "privateClusterEnabled" {
  description = "This provides Private IP Address for the Kubernetes API on the Virtual Network where the Kubernetes Cluster is located"
  type        = bool
}
variable "dnsPrefix" {
  description = "DNS prefix specified when creating the managed cluster"
  type        = string
}
variable "networkPlugin" {
  description = "Network plugin to use for networking"
  type        = string
}
variable "serviceCidr" {
  description = "The Network Range used by the Kubernetes service"
  type        = string
}
variable "dnsServiceIp" {
  description = "IP address within the Kubernetes service address range that will be used by cluster service discovery"
  type        = string

}
variable "vnetId" {
  description = "ID of a Subnet where the Kubernetes Node Pool should exist"
  type        = string
}
variable "osSku" {
  description = "Specifies the OS SKU used by the agent pool"
  type        = string
}
variable "osDiskType" {
  description = "The type of disk which should be used for the Operating System"
  type        = string
}
variable "osDiskSizeGb" {
  description = "The size of the OS Disk which should be used for each agent in the Node Pool"
  type        = string
}
variable "nodePoolZones" {
  description = "Specifies a list of Availability Zones in which this Kubernetes Cluster should be located"
  type        = set(string)
}
variable "nodeCount" {
  description = "The initial number of nodes which should exist in this Node Pool"
  type        = number
}
variable "enableAutoScaling" {
  description = "Parameter that defines whether autoscaling has been enabled or not"
  type        = bool
}
variable "systemMaxCount" {
  description = "Defines Maximum number ofNodes that should exist in this Node Pool"
  type        = number
}
variable "systemMinCount" {
  description = "Defines Minium number ofNodes that should exist in this Node Pool"
  type        = number
}
variable "systemMaxPods" {
  description = "Defines Maximum number of Pods that run on each Agent"
  type        = number
}
variable "identityType" {
  description = "Specifies the type of Managed Service Identity that should be configured on this Kubernetes Cluster"
  type        = string
}
variable "aksTags" {
  description = "A mapping of tags to assign to AKS"
  type        = map(string)
}
variable "node_resource_group" {
  description = "The auto-generated Resource Group which contains the resources for this Kubernetes Cluster"
  type        = string
}
variable "privateClusterPublicFqdnEnabled" {
  description = "Specifies whether a Public FQDN for this Private Cluster should be added"
  type        = bool

}
# variable "nodePool" {
#   description = "Defines the Node Pool Configuration which is present inside Kubernetes Cluster"
#   type = map(object({
#     name                = string
#     mode                = string
#     vm_size             = string
#     os_type             = string
#     os_sku              = string
#     os_disk_type        = string
#     os_disk_size_gb     = string
#     enable_auto_scaling = bool
#     node_count          = number
#     max_count           = number
#     min_count           = number
#     zones               = list(string)
#     max_pods            = number
#     node_labels         = map(string)
#   }))
# }