resource "azurerm_windows_function_app" "functionAppWindows" {
  name                          = var.faName
  resource_group_name           = var.rgName
  location                      = var.location
  storage_account_name          = var.storageAccountName
  storage_account_access_key    = var.storageAccountaccesskey
  service_plan_id               = var.aspId
  public_network_access_enabled = var.publicNetworkAccessEnabled
  tags                          = var.tags
  virtual_network_subnet_id     = var.virtualNetworkSubnetId
  # app_settings = {
  #   "FUNCTIONS_WORKER_RUNTIME" = var.functionsWorkerRuntime
  # }
  site_config {
    always_on                = var.alwaysOn
    vnet_route_all_enabled   = var.vnetRouteAllEnabled
    use_32_bit_worker        = var.use32BitWorker
    elastic_instance_minimum = var.elasticInstanceMinimum
    dynamic "application_stack" {
      for_each = var.dotnetStack == {} ? [] : [null]
      content {
        dotnet_version = var.dotnetStack.dotnetVersion
      }
    }
  }
  identity { type = var.identityType }
  lifecycle { ignore_changes = [] }
}
