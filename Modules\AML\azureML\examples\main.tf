// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "azureML" {
  source   = "../" // Modify module path accordingly 
  location = "Central India"
  rgName   = "Demo-RG"
  
  azureMLSettings = {
    workspaceName         = "demo-aml-workspace"
    applicationInsightsId = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Insights/components/demo-app-insights" // Associate Application Insights ID on this Place with the help of either Module (using Output), Locals or Resource Block
    keyVaultId            = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.KeyVault/vaults/demo-key-vault" // Associate Key Vault ID on this Place with the help of either Module (using Output), Locals or Resource Block
    storageAccountId      = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Storage/storageAccounts/demostorageaccount" // Associate Storage Account ID on this Place with the help of either Module (using Output), Locals or Resource Block
    containerRegistryId   = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.ContainerRegistry/registries/ContainerRegistry" // Associate Container Registry ID on this Place with the help of either Module (using Output), Locals or Resource Block
  }
  
  azureMLConfig = {
    publicNetworkAccessEnabled = true
    imageBuildComputeName      = "buildcompute"
    friendlyName               = "Demo AML Workspace"
    highBusinessImpact         = false
    description                = "Azure Machine Learning Workspace for Demo purposes"
  }
  
  identityType = "SystemAssigned"
  
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}