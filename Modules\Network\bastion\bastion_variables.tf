variable "rgName" {
  description = "Name of the resource group where the Bastion Host will be created"
  type        = string
}

variable "location" {
  description = "Azure region where the Bastion Host will be created"
  type        = string
}

variable "bastionSettings" {
  description = "Core settings for the Bastion Host"
  type = object({
    bastionName         = string
    sku                 = string
    ipConfigurationName = string
    scaleUnits          = number # The number of scale units with which to provision the Bastion Host
  })
  validation {
    condition     = contains(["Basic", "Standard"], var.bastionSettings.sku)
    error_message = "The sku must be either 'Basic' or 'Standard'."
  }
}

variable "subnetId" {
  description = "The ID of the AzureBastionSubnet subnet"
  type        = string
}

variable "publicIpAddressId" {
  description = "The ID of the Public IP Address for the Bastion Host"
  type        = string
}

variable "bastionConfig" {
  description = "Feature configuration for the Bastion Host"
  type = object({
    copyPasteEnabled = optional(bool, true)
    fileCopyEnabled  = optional(bool, false)
    tunnelingEnabled = optional(bool, false)
  })
  default = {}
}

variable "tags" {
  description = "Tags to be applied to the Bastion Host"
  type        = map(string)
  default     = {}
}