// REQUIRED VARIABLES (variables which are needed to be passed)
variable "nsgName" {
  type        = string
  description = "Name of the Azure Network Security Group"
}
variable "location" {
  type        = string
  description = "The location (region) where the Azure Network Security Group will be created"
}
variable "rgName" {
  type        = string
  description = "The name of the Azure Resource Group where the Network Security Group will be created"
}
variable "secRule" {
  description = "Network Security Group Rules with attributes"
  type = list(object({
    name                     = string
    priority                 = number
    direction                = string
    access                   = string
    protocol                 = string
    sourcePortRange          = string
    destinationPortRange     = string
    sourceAddressPrefix      = string
    destinationAddressPrefix = string
  }))
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
