// REQUIRED VARIABLES (variables which are needed to be passed)
variable "privateEndpointName" {
  description = "Name of the Azure Private Endpoint"
  type        = string
}
variable "location" {
  description = "The region where the Private Endpoint will be created"
  type        = string
}
variable "rgName" {
  description = "The name of the resource group where the Private Endpoint will be created"
  type        = string
}
variable "subnetEndpointId" {
  description = "The ID of the subnet for the Endpoint"
  type        = string
}
variable "privateServiceConnectionName" {
  description = "Name of the Private Service Connection"
  type        = string
}
variable "isManualConnection" {
  description = "Boolean indicating if the connection is manual"
  type        = bool
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "privateConnectionResourceId" {
  description = "Resource ID for the private connection"
  type        = string
}
variable "privateConnectionSubresourceNames" {
  description = "Names of the subresources for the private connection"
  type        = list(string)
}
variable "privateDnsZoneGroupName" {
  description = "Name of the private DNS zone group"
  type        = string
}
variable "privateDnsZoneIds" {
  description = "The IDs of the private DNS zones"
  type        = list(string)
}

