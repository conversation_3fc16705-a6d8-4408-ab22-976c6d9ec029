// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "aks" {
  source                          = "../" // Modifiy module path accordingly 
  aksName                         = "AKS"
  location                        = "Central India"
  rgName                          = "Demo-RG"
  skuTier                         = "Standard"
  privateClusterEnabled           = false
  privateClusterPublicFqdnEnabled = false
  dnsPrefix                       = "aksdemoprefix"
  node_resource_group             = "AKS-Demo-RG"
  networkPlugin                   = "azure"
  serviceCidr                     = "10.0.0.0/26"
  dnsServiceIp                    = "********"
  nodePoolName                    = "systempool"
  vnetId                          = "Pass_VNet_ID" // Associate Virtual Network Id on this Place with the help of either Module, Locals or Resource Block
  vmSize                          = "Standard_Ds2_v2"
  osSku                           = "Ubuntu"
  osDiskType                      = "Managed"
  osDiskSizeGb                    = 128
  nodePoolZones                   = ["1", "2", "3"]
  nodeCount                       = 3
  enableAutoScaling               = true
  systemMaxCount                  = 5
  systemMinCount                  = 1
  systemMaxPods                   = 30
  identityType                    = "SystemAssigned"
  aksTags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  # nodePool = {
  #   userPool = {
  #     name                = "userpool"
  #     mode                = "User"
  #     vm_size             = "Standard_DS2_v2"
  #     os_type             = "Linux"
  #     os_sku              = "Ubuntu"
  #     os_disk_type        = "Managed"
  #     os_disk_size_gb     = 128
  #     enable_auto_scaling = true
  #     node_count = 3
  #     max_count           = 5
  #     min_count           = 1
  #     zones               = ["1", "2", "3"]
  #     max_pods            = 30
  #     node_labels = {
  #       "environment" = "production"
  #       "workload"    = "general"
  #     }
  #  }
  # }
}