# Hub and Spoke Network Infrastructure

This Terraform configuration creates a hub and spoke network architecture in Azure using for_each patterns for all modules, making it fully configurable via terraform.tfvars.

## Architecture Overview

### Virtual Networks
- **Hub VNet** (10.0.0.0/16): Central network with shared services
- **Spoke VNet** (10.1.0.0/16): Workload network connected to hub

### Subnets

#### Hub VNet Subnets
- **Hub-Normal-Subnet** (10.0.1.0/24): General purpose subnet with NSG and Route Table
- **AzureBastionSubnet** (10.0.2.0/24): Azure Bastion subnet
- **AzureFirewallSubnet** (10.0.3.0/24): Azure Firewall subnet

#### Spoke VNet Subnets
- **ML-Compute-Subnet** (10.1.1.0/24): No NSG/RT - For ML workloads
- **AKS-Subnet** (10.1.2.0/24): No NSG/RT - For Azure Kubernetes Service
- **Compute-Subnet** (10.1.3.0/24): With NSG and Route Table - For general compute workloads
- **VNet-Integration-Subnet** (10.1.4.0/24): With NSG and Route Table - For VNet integration services
- **Private-Endpoint-Subnet** (10.1.5.0/24): No NSG/RT - For private endpoints

### Network Security Groups
- **Hub-Shared-Services-NSG**: Applied to hub normal subnet (allows HTTP/HTTPS)
- **Spoke-Workload-NSG**: Applied to compute and VNet integration subnets (allows internal traffic)

### Route Tables
- **Hub-Shared-Services-RouteTable**: Applied to hub normal subnet (routes to spoke via firewall)
- **Spoke-Compute-Workload-RouteTable**: Applied to compute subnet (routes to hub and default via firewall)
- **Spoke-Integration-RouteTable**: Applied to VNet integration subnet (routes to hub via firewall)

### VNet Peering
- Bidirectional peering between hub and spoke VNets with forwarded traffic enabled

### Public IPs
- **Bastion-PublicIP**: For Azure Bastion Host
- **Firewall-PublicIP**: For Azure Firewall

## Usage

1. **Initialize Terraform**:
   ```bash
   terraform init
   ```

2. **Review the plan**:
   ```bash
   terraform plan
   ```

3. **Apply the configuration**:
   ```bash
   terraform apply
   ```

## Customization

All configuration is controlled via `terraform.tfvars`. You can:

- Add/remove virtual networks
- Add/remove subnets
- Modify NSG rules
- Change route table routes
- Add/remove associations
- Modify peering settings

### Example: Adding a new subnet

```hcl
subnets = {
  # ... existing subnets ...
  "new_subnet" = {
    name                                         = "New-Subnet"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
}
```

### Example: Adding NSG association for new subnet

```hcl
nsg_associations = {
  # ... existing associations ...
  "new_subnet_nsg" = {
    nsg_key    = "spoke_workload_nsg"
    subnet_key = "new_subnet"
  }
}
```

## Key Features

- **For_each implementation**: All modules use for_each for scalability
- **Flexible associations**: NSGs and Route Tables can be associated with any subnet
- **Hub-spoke topology**: Centralized security and routing through hub
- **Configurable via tfvars**: No hardcoded values in main configuration
- **Comprehensive outputs**: All resource IDs and names are exposed
- **Purpose-built subnets**: Each spoke subnet is designed for specific workload types

## Requirements

- Terraform >= 1.0
- Azure Provider >= 3.0
- Existing resource group: `kartik.jindal_RG`

## Notes

- **Compute-Subnet** and **VNet-Integration-Subnet** have both NSG and Route Table (as requested)
- **ML-Compute-Subnet**, **AKS-Subnet**, and **Private-Endpoint-Subnet** have no NSG/RT for flexibility
- Firewall IP (********) is used as next hop in route tables
- All resources are tagged for proper governance
- Bastion and Firewall subnets follow Azure naming requirements
- Subnet naming follows workload-specific conventions for better organization
