# Hub and Spoke Network Infrastructure

This Terraform configuration creates a hub and spoke network architecture in Azure using for_each patterns for all modules, making it fully configurable via terraform.tfvars.

## Architecture Overview

### Resource Groups
- **Hub-RG**: Contains all hub network resources (VNet, subnets, NSG, route table, public IPs)
- **Spoke-RG**: Contains all spoke network resources (VNet, subnets, NSG, route tables)

### Virtual Networks
- **Hub VNet** (10.0.0.0/16): Central network with shared services
- **Spoke VNet** (********/16): Workload network connected to hub

### Subnets

#### Hub VNet Subnets
- **Hub-Normal-Subnet** (********/24): General purpose subnet with NSG and Route Table
- **AzureBastionSubnet** (********/24): Azure Bastion subnet
- **AzureFirewallSubnet** (********/24): Azure Firewall subnet

#### Spoke VNet Subnets
- **ML-Compute-Subnet** (********/24): No NSG/RT - For ML workloads
- **AKS-Subnet** (********/24): No NSG/RT - For Azure Kubernetes Service
- **Compute-Subnet** (********/24): With NSG and Route Table - For general compute workloads
- **VNet-Integration-Subnet** (********/24): With NSG and Route Table - For VNet integration services
- **Private-Endpoint-Subnet** (********/24): No NSG/RT - For private endpoints

### Network Security Groups
- **Hub-Shared-Services-NSG**: Applied to hub normal subnet (allows HTTP/HTTPS)
- **Spoke-Workload-NSG**: Applied to compute and VNet integration subnets (allows internal traffic)

### Route Tables
- **Hub-Shared-Services-RouteTable**: Applied to hub normal subnet (routes to spoke via firewall)
- **Spoke-Compute-Workload-RouteTable**: Applied to compute subnet (routes to hub and default via firewall)
- **Spoke-Integration-RouteTable**: Applied to VNet integration subnet (routes to hub via firewall)

### VNet Peering
- Bidirectional peering between hub and spoke VNets with forwarded traffic enabled

### Public IPs
- **Bastion-PublicIP**: For Azure Bastion Host
- **Firewall-PublicIP**: For Azure Firewall

## Usage

1. **Initialize Terraform**:
   ```bash
   terraform init
   ```

2. **Review the plan**:
   ```bash
   terraform plan
   ```

3. **Apply the configuration**:
   ```bash
   terraform apply
   ```

## Customization

All configuration is controlled via `terraform.tfvars`. You can:

- Add/remove virtual networks
- Add/remove subnets
- Modify NSG rules
- Change route table routes
- Add/remove associations
- Modify peering settings

### Example: Adding a new subnet

```hcl
subnets = {
  # ... existing subnets ...
  "new_subnet" = {
    name                                         = "New-Subnet"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
}
```

### Example: Adding NSG association for new subnet

```hcl
nsg_associations = {
  # ... existing associations ...
  "new_subnet_nsg" = {
    nsg_key    = "spoke_workload_nsg"
    subnet_key = "new_subnet"
  }
}
```

## Key Features

- **For_each implementation**: All modules use for_each for scalability
- **Dynamic Resource Group Assignment**: Automatic resource group assignment based on resource type (hub/spoke)
- **Single Source of Truth**: Resource group names defined once in variables, automatically propagated
- **Flexible associations**: NSGs and Route Tables can be associated with any subnet
- **Hub-spoke topology**: Centralized security and routing through hub
- **Configurable via tfvars**: No hardcoded values in main configuration
- **Comprehensive outputs**: All resource IDs and names are exposed
- **Purpose-built subnets**: Each spoke subnet is designed for specific workload types
- **Maintainable Configuration**: Change resource group names in one place, automatically updates everywhere

## Dynamic Resource Group Assignment

This configuration uses Terraform locals to automatically assign the correct resource group to each resource based on its type:

### Assignment Logic
- **Hub Resources**: Automatically assigned to the hub resource group
  - Hub VNet (type = "hub")
  - All subnets starting with "hub_"
  - Hub NSG ("hub_shared_services_nsg")
  - Hub Route Table ("hub_shared_services_rt")
  - All Public IPs (bastion, firewall)
  - Hub-to-Spoke VNet Peering

- **Spoke Resources**: Automatically assigned to the spoke resource group
  - Spoke VNet (type = "spoke")
  - All subnets starting with spoke subnet names
  - Spoke NSG ("spoke_workload_nsg")
  - Spoke Route Tables (all others)
  - Spoke-to-Hub VNet Peering

### Benefits
- **Single Source of Truth**: Change resource group names only in the `resource_groups` variable
- **Automatic Propagation**: All resources automatically use the correct resource group
- **Reduced Errors**: No risk of mismatched resource group names
- **Easy Maintenance**: Rename resource groups without touching individual resource configurations

## Requirements

- Terraform >= 1.0
- Azure Provider >= 3.0
- Azure subscription with appropriate permissions to create resource groups and network resources

## Notes

- **Compute-Subnet** and **VNet-Integration-Subnet** have both NSG and Route Table (as requested)
- **ML-Compute-Subnet**, **AKS-Subnet**, and **Private-Endpoint-Subnet** have no NSG/RT for flexibility
- Firewall IP (********) is used as next hop in route tables
- All resources are tagged for proper governance
- Bastion and Firewall subnets follow Azure naming requirements
- Subnet naming follows workload-specific conventions for better organization
