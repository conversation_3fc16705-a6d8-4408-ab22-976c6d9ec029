// REQ<PERSON>RED VARIABLES
variable "location" {
  description = "Azure region where the Machine Learning Workspace will be created"
  type        = string
}

variable "rgName" {
  description = "Name of the resource group where the Machine Learning Workspace will be created"
  type        = string
}

variable "azureMLSettings" {
  description = "Core settings for the Azure Machine Learning Workspace"
  type = object({
    workspaceName         = string
    applicationInsightsId = string
    keyVaultId            = string
    storageAccountId      = string
    containerRegistryId   = string
  })
}

// OPTIONAL VARIABLES
variable "azureMLConfig" {
  description = "Configuration options for the Azure Machine Learning Workspace"
  type = object({
    publicNetworkAccessEnabled = optional(bool, true)
    imageBuildComputeName      = optional(string, null)
    friendlyName               = optional(string, null)
    highBusinessImpact         = optional(bool, false)
    description                = optional(string, null)
  })
  default = {}
}

variable "identityType" {
  description = "The type of identity to use for the Azure Machine Learning Workspace"
  type        = string
  default     = "SystemAssigned"
  validation {
    condition     = contains(["SystemAssigned", "UserAssigned", "SystemAssigned, UserAssigned"], var.identityType)
    error_message = "The identity type must be one of: SystemAssigned, UserAssigned, or 'SystemAssigned, UserAssigned'."
  }
}

variable "identityIds" {
  description = "A list of user assigned identity IDs to be assigned to the Azure Machine Learning Workspace"
  type        = list(string)
  default     = []
}

variable "encryption" {
  description = "Encryption settings for the Azure Machine Learning Workspace"
  type = object({
    keyVaultId = string
    keyId      = string
  })
  default = null
}

variable "primaryUserAssignedIdentity" {
  description = "Primary user assigned identity for the Azure Machine Learning Workspace"
  type = object({
    userAssignedIdentityId = string
  })
  default = null
}

variable "tags" {
  description = "Tags to be applied to the Azure Machine Learning Workspace"
  type        = map(string)
  default     = {}
}