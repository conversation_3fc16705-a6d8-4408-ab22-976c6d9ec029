# Hub Infrastructure Deployment

This Terraform configuration deploys core Azure infrastructure resources in the Hub Resource Group using for_each patterns, consistent with the established hub-spoke network architecture.

## Architecture Overview

### Deployed Resources

**Core Infrastructure:**
- **Azure Firewall** - Network security and traffic filtering
- **Azure Bastion** - Secure RDP/SSH access to VMs
- **Application Gateway** - Application load balancing and web application firewall

**Private DNS Infrastructure:**
- **8 Private DNS Zones** for Azure services:
  - Azure Container Registry (ACR)
  - Azure Machine Learning (AML)
  - Key Vault
  - AI Search (Cognitive Search)
  - Cosmos DB
  - Azure Kubernetes Service (AKS)
  - Storage Account
  - Azure Data Factory (ADF)
- **VNet Links** connecting all DNS zones to the Hub VNet

### Resource Organization

**Hub Resource Group (Hub-RG):**
- All infrastructure resources are deployed here
- Leverages existing network infrastructure (VNet, subnets, public IPs)
- Maintains consistency with hub-spoke architecture

**Network Integration:**
- Azure Firewall uses AzureFirewallSubnet and Firewall-PublicIP
- Azure Bastion uses AzureBastionSubnet and Bastion-PublicIP
- Application Gateway uses Hub-Normal-Subnet with dedicated public IP
- All Private DNS zones are linked to Hub-VNet

## Key Features

- **For_each Implementation**: All modules use for_each for scalability
- **Infrastructure References**: Leverages existing network infrastructure via data sources
- **Automatic Dependencies**: Proper resource creation order and dependencies
- **Comprehensive Tagging**: Consistent tagging strategy across all resources
- **Configurable via tfvars**: Complete control through terraform.tfvars
- **Private DNS Integration**: Ready for private endpoint deployments

## Prerequisites

1. **Existing Network Infrastructure**: 
   - Hub-RG resource group must exist
   - Hub-VNet with required subnets must be deployed
   - Public IPs for Firewall and Bastion must exist

2. **Terraform Requirements**:
   - Terraform >= 1.0
   - Azure Provider >= 4.0
   - Appropriate Azure permissions

## Usage

### 1. Initialize Terraform
```bash
cd Workload/Core/RG
terraform init
```

### 2. Review Configuration
```bash
terraform plan
```

### 3. Deploy Infrastructure
```bash
terraform apply
```

### 4. Verify Deployment
```bash
terraform output
```

## Configuration Structure

### terraform.tfvars
- **azure_firewalls**: Firewall configuration
- **azure_bastions**: Bastion host configuration  
- **application_gateways**: Application Gateway configuration
- **private_dns_zones**: DNS zone definitions
- **vnet_links**: VNet link configurations

### Key Configuration Points

**Azure Firewall:**
- Standard tier with zone redundancy
- Integrated with existing firewall subnet and public IP

**Azure Bastion:**
- Standard SKU with advanced features enabled
- Scale units configured for performance

**Application Gateway:**
- Standard_v2 tier with auto-scaling
- Dedicated public IP created automatically

**Private DNS Zones:**
- Service-specific zones for Azure PaaS services
- Linked to Hub VNet for private endpoint resolution

## Customization

### Adding New Private DNS Zones
```hcl
private_dns_zones = {
  "new_service_dns_zone" = {
    name = "privatelink.newservice.azure.com"
    tags = {
      Service = "New Azure Service"
      Purpose = "Private DNS Resolution"
    }
  }
}

vnet_links = {
  "new_service_vnet_link" = {
    name                 = "newservice-hub-vnet-link"
    dns_zone_key        = "new_service_dns_zone"
    registration_enabled = false
    tags = {
      Service = "New Azure Service"
      Purpose = "VNet Integration"
    }
  }
}
```

### Scaling Resources
- Modify capacity, scale_units, or zones in terraform.tfvars
- Add additional instances by adding new keys to the maps

## Outputs

The configuration provides comprehensive outputs including:
- Resource IDs and names for all deployed resources
- Hub infrastructure summary
- Specific DNS zone IDs for private endpoint integration
- Network integration details

## Dependencies

This configuration depends on the network infrastructure deployed via:
- `Workload/Network/vnet/` - Hub-spoke network architecture

## Security Considerations

- All resources deployed in Hub-RG for centralized management
- Private DNS zones enable secure private endpoint connectivity
- Firewall provides network-level security controls
- Bastion provides secure administrative access

## Next Steps

After deployment, you can:
1. Configure firewall rules and policies
2. Deploy private endpoints using the created DNS zones
3. Configure Application Gateway backend pools
4. Set up monitoring and alerting
5. Deploy workload resources in spoke networks
