// REQUIRED VARIABLES (variables which are needed to be passed)
variable "privateDnsZoneName" {
  description = "The name which should be used for this Private DNS Zone"
  type        = string
}
variable "rgName" {
  description = "The name of the resource group where the Private DNS Zone will be created"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}