resource "azurerm_data_factory_integration_runtime_self_hosted" "shirAdf" {
  name            = var.selfHostedIRName
  data_factory_id = var.dataFactoryId
  # This dynamic block configures RBAC authorization for the self-hosted integration runtime, allowing multiple RBAC authorization settings to be specified, with each containing a resource ID.
  dynamic "rbac_authorization" {
    for_each = var.rbacAuthorization
    content {
      resource_id = rbac_authorization.value.resourceId
    }
  }
}
