// REQUIRED VARIABLES (variables which are needed to be passed)
variable "acrName" {
  description = "The name of the Azure Container Registry"
  type        = string
}
variable "rgName" {
  description = "The name of the Azure Resource Group where the Azure Container Registry will be provisioned"
  type        = string
}
variable "location" {
  description = "The location (region) where the Azure Container Registry will be provisioned"
  type        = string
}
variable "acrSku" {
  description = "The SKU which determines its pricing tier and capabilities"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "publicNetworkAccessEnabled" {
  description = "Determines whether public network access to the Azure Container Registry is enabled"
  type        = bool
}
variable "zoneRedundancyEnabled" {
  description = "It defines whether zone redundancy is enabled or not"
  type        = bool
}
variable "acrTags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "networkRuleBypassOption" {
  description = "It defines whether to allow trusted Azure services to access a network restricted Container Registry or not"
  type        = string
}
variable "defaultAction" {
  description = "Used to monitor the behaviour for requests matching no rules"
  type        = string
}