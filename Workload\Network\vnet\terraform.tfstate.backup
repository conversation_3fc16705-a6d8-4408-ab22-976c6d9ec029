{"version": 4, "terraform_version": "1.10.3", "serial": 26, "lineage": "1131a5ae-c77c-eb0d-ec94-b17500b01622", "outputs": {"aks_subnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/AKS-Subnet", "type": "string"}, "bastion_public_ip_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/publicIPAddresses/Bastion-PublicIP", "type": "string"}, "bastion_subnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/AzureBastionSubnet", "type": "string"}, "compute_subnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "type": "string"}, "firewall_public_ip_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/publicIPAddresses/Firewall-PublicIP", "type": "string"}, "firewall_subnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/AzureFirewallSubnet", "type": "string"}, "hub_vnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet", "type": "string"}, "ml_compute_subnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/ML-Compute-Subnet", "type": "string"}, "nsg_association_ids": {"value": {"compute_subnet_nsg": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "hub_normal_subnet_nsg": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "vnet_integration_subnet_nsg": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet"}, "type": ["object", {"compute_subnet_nsg": "string", "hub_normal_subnet_nsg": "string", "vnet_integration_subnet_nsg": "string"}]}, "nsg_ids": {"value": {"hub_shared_services_nsg": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/networkSecurityGroups/Hub-Shared-Services-NSG", "spoke_workload_nsg": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/networkSecurityGroups/Spoke-Workload-NSG"}, "type": ["object", {"hub_shared_services_nsg": "string", "spoke_workload_nsg": "string"}]}, "nsg_names": {"value": {"hub_shared_services_nsg": "Hub-Shared-Services-NSG", "spoke_workload_nsg": "Spoke-Workload-NSG"}, "type": ["object", {"hub_shared_services_nsg": "string", "spoke_workload_nsg": "string"}]}, "private_endpoint_subnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Private-Endpoint-Subnet", "type": "string"}, "public_ip_ids": {"value": {"bastion_pip": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/publicIPAddresses/Bastion-PublicIP", "firewall_pip": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/publicIPAddresses/Firewall-PublicIP"}, "type": ["object", {"bastion_pip": "string", "firewall_pip": "string"}]}, "route_table_association_ids": {"value": {"compute_subnet_rt": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "hub_normal_subnet_rt": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "vnet_integration_subnet_rt": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet"}, "type": ["object", {"compute_subnet_rt": "string", "hub_normal_subnet_rt": "string", "vnet_integration_subnet_rt": "string"}]}, "route_table_ids": {"value": {"hub_shared_services_rt": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Hub-Shared-Services-RouteTable", "spoke_compute_workload_rt": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Spoke-Compute-Workload-RouteTable", "spoke_integration_rt": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Spoke-Integration-RouteTable"}, "type": ["object", {"hub_shared_services_rt": "string", "spoke_compute_workload_rt": "string", "spoke_integration_rt": "string"}]}, "route_table_names": {"value": {"hub_shared_services_rt": "Hub-Shared-Services-RouteTable", "spoke_compute_workload_rt": "Spoke-Compute-Workload-RouteTable", "spoke_integration_rt": "Spoke-Integration-RouteTable"}, "type": ["object", {"hub_shared_services_rt": "string", "spoke_compute_workload_rt": "string", "spoke_integration_rt": "string"}]}, "spoke_vnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet", "type": "string"}, "subnet_ids": {"value": {"aks_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/AKS-Subnet", "compute_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "hub_bastion_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/AzureBastionSubnet", "hub_firewall_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/AzureFirewallSubnet", "hub_normal_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "ml_compute_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/ML-Compute-Subnet", "private_endpoint_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Private-Endpoint-Subnet", "vnet_integration_subnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet"}, "type": ["object", {"aks_subnet": "string", "compute_subnet": "string", "hub_bastion_subnet": "string", "hub_firewall_subnet": "string", "hub_normal_subnet": "string", "ml_compute_subnet": "string", "private_endpoint_subnet": "string", "vnet_integration_subnet": "string"}]}, "subnet_names": {"value": {"aks_subnet": "AKS-Subnet", "compute_subnet": "Compute-Subnet", "hub_bastion_subnet": "AzureBastionSubnet", "hub_firewall_subnet": "AzureFirewallSubnet", "hub_normal_subnet": "Hub-Normal-Subnet", "ml_compute_subnet": "ML-Compute-Subnet", "private_endpoint_subnet": "Private-Endpoint-Subnet", "vnet_integration_subnet": "VNet-Integration-Subnet"}, "type": ["object", {"aks_subnet": "string", "compute_subnet": "string", "hub_bastion_subnet": "string", "hub_firewall_subnet": "string", "hub_normal_subnet": "string", "ml_compute_subnet": "string", "private_endpoint_subnet": "string", "vnet_integration_subnet": "string"}]}, "virtual_network_ids": {"value": {"hub_vnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet", "spoke_vnet": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet"}, "type": ["object", {"hub_vnet": "string", "spoke_vnet": "string"}]}, "virtual_network_names": {"value": {"hub_vnet": "Hub-VNet", "spoke_vnet": "Spoke-VNet"}, "type": ["object", {"hub_vnet": "string", "spoke_vnet": "string"}]}, "vnet_integration_subnet_id": {"value": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet", "type": "string"}}, "resources": [{"module": "module.network_security_groups[\"hub_shared_services_nsg\"]", "mode": "managed", "type": "azurerm_network_security_group", "name": "nsg", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/networkSecurityGroups/Hub-Shared-Services-NSG", "location": "centralindia", "name": "Hub-Shared-Services-NSG", "resource_group_name": "kartik.jindal_RG", "security_rule": [{"access": "Allow", "description": "", "destination_address_prefix": "*", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "443", "destination_port_ranges": [], "direction": "Inbound", "name": "Allow-HTTPS", "priority": 110, "protocol": "Tcp", "source_address_prefix": "*", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}, {"access": "Allow", "description": "", "destination_address_prefix": "*", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "80", "destination_port_ranges": [], "direction": "Inbound", "name": "Allow-HTTP", "priority": 100, "protocol": "Tcp", "source_address_prefix": "*", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}], "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Purpose": "Shared Services", "Type": "<PERSON><PERSON>"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.network_security_groups[\"spoke_workload_nsg\"]", "mode": "managed", "type": "azurerm_network_security_group", "name": "nsg", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/networkSecurityGroups/Spoke-Workload-NSG", "location": "centralindia", "name": "Spoke-Workload-NSG", "resource_group_name": "kartik.jindal_RG", "security_rule": [{"access": "Allow", "description": "", "destination_address_prefix": "10.0.0.0/8", "destination_address_prefixes": [], "destination_application_security_group_ids": [], "destination_port_range": "*", "destination_port_ranges": [], "direction": "Inbound", "name": "Allow-Internal", "priority": 100, "protocol": "*", "source_address_prefix": "10.0.0.0/8", "source_address_prefixes": [], "source_application_security_group_ids": [], "source_port_range": "*", "source_port_ranges": []}], "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Purpose": "Workload Protection", "Type": "Spoke"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.nsg_associations[\"compute_subnet_nsg\"]", "mode": "managed", "type": "azurerm_subnet_network_security_group_association", "name": "nsgAssociation", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "network_security_group_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/networkSecurityGroups/Spoke-Workload-NSG", "subnet_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMH19", "dependencies": ["module.network_security_groups.azurerm_network_security_group.nsg", "module.subnets.azurerm_subnet.subnets", "module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.nsg_associations[\"hub_normal_subnet_nsg\"]", "mode": "managed", "type": "azurerm_subnet_network_security_group_association", "name": "nsgAssociation", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "network_security_group_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/networkSecurityGroups/Hub-Shared-Services-NSG", "subnet_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMH19", "dependencies": ["module.network_security_groups.azurerm_network_security_group.nsg", "module.subnets.azurerm_subnet.subnets", "module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.nsg_associations[\"vnet_integration_subnet_nsg\"]", "mode": "managed", "type": "azurerm_subnet_network_security_group_association", "name": "nsgAssociation", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet", "network_security_group_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/networkSecurityGroups/Spoke-Workload-NSG", "subnet_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMH19", "dependencies": ["module.network_security_groups.azurerm_network_security_group.nsg", "module.subnets.azurerm_subnet.subnets", "module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.public_ips[\"bastion_pip\"]", "mode": "managed", "type": "azurerm_public_ip", "name": "pip", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"allocation_method": "Static", "ddos_protection_mode": "VirtualNetworkInherited", "ddos_protection_plan_id": null, "domain_name_label": null, "edge_zone": "", "fqdn": null, "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/publicIPAddresses/Bastion-PublicIP", "idle_timeout_in_minutes": 4, "ip_address": "*************", "ip_tags": null, "ip_version": "IPv4", "location": "centralindia", "name": "Bastion-PublicIP", "public_ip_prefix_id": null, "resource_group_name": "kartik.jindal_RG", "reverse_fqdn": null, "sku": "Standard", "sku_tier": "Regional", "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Purpose": "Bastion"}, "timeouts": null, "zones": ["1"]}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.public_ips[\"firewall_pip\"]", "mode": "managed", "type": "azurerm_public_ip", "name": "pip", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"allocation_method": "Static", "ddos_protection_mode": "VirtualNetworkInherited", "ddos_protection_plan_id": null, "domain_name_label": null, "edge_zone": "", "fqdn": null, "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/publicIPAddresses/Firewall-PublicIP", "idle_timeout_in_minutes": 4, "ip_address": "**************", "ip_tags": null, "ip_version": "IPv4", "location": "centralindia", "name": "Firewall-PublicIP", "public_ip_prefix_id": null, "resource_group_name": "kartik.jindal_RG", "reverse_fqdn": null, "sku": "Standard", "sku_tier": "Regional", "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Purpose": "Firewall"}, "timeouts": null, "zones": ["1"]}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.route_table_associations[\"compute_subnet_rt\"]", "mode": "managed", "type": "azurerm_subnet_route_table_association", "name": "rtAssociation", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "route_table_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Spoke-Compute-Workload-RouteTable", "subnet_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMH19", "dependencies": ["module.route_tables.azurerm_route_table.routeTable", "module.subnets.azurerm_subnet.subnets", "module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.route_table_associations[\"hub_normal_subnet_rt\"]", "mode": "managed", "type": "azurerm_subnet_route_table_association", "name": "rtAssociation", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "route_table_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Hub-Shared-Services-RouteTable", "subnet_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMH19", "dependencies": ["module.route_tables.azurerm_route_table.routeTable", "module.subnets.azurerm_subnet.subnets", "module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.route_table_associations[\"vnet_integration_subnet_rt\"]", "mode": "managed", "type": "azurerm_subnet_route_table_association", "name": "rtAssociation", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet", "route_table_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Spoke-Integration-RouteTable", "subnet_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet", "timeouts": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjE4MDAwMDAwMDAwMDAsInJlYWQiOjMwMDAwMDAwMDAwMH19", "dependencies": ["module.route_tables.azurerm_route_table.routeTable", "module.subnets.azurerm_subnet.subnets", "module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.route_tables[\"hub_shared_services_rt\"]", "mode": "managed", "type": "azurerm_route_table", "name": "routeTable", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"bgp_route_propagation_enabled": false, "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Hub-Shared-Services-RouteTable", "location": "centralindia", "name": "Hub-Shared-Services-RouteTable", "resource_group_name": "kartik.jindal_RG", "route": [{"address_prefix": "********/16", "name": "ToSpoke", "next_hop_in_ip_address": "********", "next_hop_type": "VirtualAppliance"}], "subnets": [], "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Purpose": "Shared Services Routing", "Type": "<PERSON><PERSON>"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.route_tables[\"spoke_compute_workload_rt\"]", "mode": "managed", "type": "azurerm_route_table", "name": "routeTable", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"bgp_route_propagation_enabled": false, "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Spoke-Compute-Workload-RouteTable", "location": "centralindia", "name": "Spoke-Compute-Workload-RouteTable", "resource_group_name": "kartik.jindal_RG", "route": [{"address_prefix": "0.0.0.0/0", "name": "<PERSON><PERSON><PERSON>", "next_hop_in_ip_address": "********", "next_hop_type": "VirtualAppliance"}, {"address_prefix": "10.0.0.0/16", "name": "ToHub", "next_hop_in_ip_address": "********", "next_hop_type": "VirtualAppliance"}], "subnets": [], "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Purpose": "Compute Workload Routing", "Type": "Spoke"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.route_tables[\"spoke_integration_rt\"]", "mode": "managed", "type": "azurerm_route_table", "name": "routeTable", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"bgp_route_propagation_enabled": false, "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/routeTables/Spoke-Integration-RouteTable", "location": "centralindia", "name": "Spoke-Integration-RouteTable", "resource_group_name": "kartik.jindal_RG", "route": [{"address_prefix": "10.0.0.0/16", "name": "ToHub", "next_hop_in_ip_address": "********", "next_hop_type": "VirtualAppliance"}], "subnets": [], "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Purpose": "VNet Integration Routing", "Type": "Spoke"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.subnets[\"aks_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/AKS-Subnet", "name": "AKS-Subnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Spoke-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.subnets[\"compute_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Compute-Subnet", "name": "Compute-Subnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Spoke-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.subnets[\"hub_bastion_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/AzureBastionSubnet", "name": "AzureBastionSubnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Hub-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.subnets[\"hub_firewall_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/AzureFirewallSubnet", "name": "AzureFirewallSubnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Hub-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.subnets[\"hub_normal_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/subnets/Hub-Normal-Subnet", "name": "Hub-Normal-Subnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Hub-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.subnets[\"ml_compute_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/ML-Compute-Subnet", "name": "ML-Compute-Subnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Spoke-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.subnets[\"private_endpoint_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/Private-Endpoint-Subnet", "name": "Private-Endpoint-Subnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Spoke-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.subnets[\"vnet_integration_subnet\"]", "mode": "managed", "type": "azurerm_subnet", "name": "subnets", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_prefixes": ["********/24"], "default_outbound_access_enabled": true, "delegation": [], "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/subnets/VNet-Integration-Subnet", "name": "VNet-Integration-Subnet", "private_endpoint_network_policies": "Disabled", "private_link_service_network_policies_enabled": true, "resource_group_name": "kartik.jindal_RG", "service_endpoint_policy_ids": null, "service_endpoints": null, "timeouts": null, "virtual_network_name": "Spoke-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.virtual_networks[\"hub_vnet\"]", "mode": "managed", "type": "azurerm_virtual_network", "name": "virtualNetwork", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_space": ["10.0.0.0/16"], "bgp_community": "", "ddos_protection_plan": [], "dns_servers": [], "edge_zone": "", "encryption": [], "flow_timeout_in_minutes": 0, "guid": "d963978f-7eeb-403c-9d52-61dcd110554c", "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet", "location": "centralindia", "name": "Hub-VNet", "resource_group_name": "kartik.jindal_RG", "subnet": [], "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Type": "<PERSON><PERSON>"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.virtual_networks[\"spoke_vnet\"]", "mode": "managed", "type": "azurerm_virtual_network", "name": "virtualNetwork", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"address_space": ["********/16"], "bgp_community": "", "ddos_protection_plan": [], "dns_servers": [], "edge_zone": "", "encryption": [], "flow_timeout_in_minutes": 0, "guid": "38f53475-054c-440e-99fe-e91fecfbe4e5", "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet", "location": "centralindia", "name": "Spoke-VNet", "resource_group_name": "kartik.jindal_RG", "subnet": [], "tags": {"Department": "Cloud Infra & Security", "Environment": "Production", "Type": "Spoke"}, "timeouts": null}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************"}]}, {"module": "module.vnet_peering[\"hub_to_spoke\"]", "mode": "managed", "type": "azurerm_virtual_network_peering", "name": "virtualNetworkPeering", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"allow_forwarded_traffic": true, "allow_gateway_transit": false, "allow_virtual_network_access": true, "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet/virtualNetworkPeerings/hub-to-spoke-peering", "local_subnet_names": null, "name": "hub-to-spoke-peering", "only_ipv6_peering_enabled": false, "peer_complete_virtual_networks_enabled": true, "remote_subnet_names": null, "remote_virtual_network_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet", "resource_group_name": "kartik.jindal_RG", "timeouts": null, "triggers": null, "use_remote_gateways": false, "virtual_network_name": "Hub-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}, {"module": "module.vnet_peering[\"spoke_to_hub\"]", "mode": "managed", "type": "azurerm_virtual_network_peering", "name": "virtualNetworkPeering", "provider": "provider[\"registry.terraform.io/hashicorp/azurerm\"]", "instances": [{"schema_version": 0, "attributes": {"allow_forwarded_traffic": true, "allow_gateway_transit": false, "allow_virtual_network_access": true, "id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Spoke-VNet/virtualNetworkPeerings/spoke-to-hub-peering", "local_subnet_names": null, "name": "spoke-to-hub-peering", "only_ipv6_peering_enabled": false, "peer_complete_virtual_networks_enabled": true, "remote_subnet_names": null, "remote_virtual_network_id": "/subscriptions/d833250a-cb43-419a-bf49-27b8942a3335/resourceGroups/kartik.jindal_RG/providers/Microsoft.Network/virtualNetworks/Hub-VNet", "resource_group_name": "kartik.jindal_RG", "timeouts": null, "triggers": null, "use_remote_gateways": false, "virtual_network_name": "Spoke-VNet"}, "sensitive_attributes": [], "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.virtual_networks.azurerm_virtual_network.virtualNetwork"]}]}], "check_results": null}