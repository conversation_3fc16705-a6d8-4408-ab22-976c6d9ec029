// REQUIRED VARIABLES (variables which are needed to be passed)
variable "natGatewayName" {
  description = "The name of the NAT Gateway"
  type        = string
}
variable "location" {
  description = "The region where the NAT Gateway will be created"
  type        = string
}
variable "rgName" {
  description = "The name of the resource group where the NAT Gateway will be created"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "skuName" {
  description = "The SKU name of the NAT Gateway"
  type        = string
}
variable "idelTimeoutInMinutes" {
  description = "The idle timeout in minutes for the NAT Gateway"
  type        = number
}
variable "zones" {
  description = "The availability zones where the NAT Gateway should be deployed"
  type        = list(string)
}
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}