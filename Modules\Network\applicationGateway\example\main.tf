// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "applicationGateway" {
  source                 = "../" // Modifiy module path accordingly 
  applicationGatewayName = "ApplicationGateway"
  rgName                 = "Demo-RG"
  location               = "Central India"
  apimTags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  sku                         = "Standard_v2"
  tier                        = "Standard_v2"
  capacity                    = 2
  gatewayIpConfiguration      = "Gateway-IP-Configuration"
  subnetId                    = "" // Associate Subnet Id on this Place with the help of either Module, Locals or Resource Block
  frontendPortName            = "FrontendPort"
  port                        = 80
  frontendIpConfigurationName = "Frontend-IP"
  publicIpId                  = "" // Associate Public IP Id on this Place with the help of either Module, Locals or Resource Block
  backendAddressPoolName      = "BackendAddressPool"
  httpSettingName             = "HttpSetting"
  cookieBasedAffinity         = "Disabled"
  path                        = "/path1/"
  protocol                    = "Http"
  requestTimeout              = 60
  listenerName                = "HttpListener"
  requestRoutingRuleName      = "RoutingRule"
  priority                    = 9
  ruleType                    = "Basic"
}