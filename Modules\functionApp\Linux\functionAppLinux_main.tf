resource "azurerm_linux_function_app" "functionAppLinux" {
  name                          = var.faName
  resource_group_name           = var.rgName
  location                      = var.location
  storage_account_name          = var.storageAccountName
  storage_account_access_key    = var.storageAccountaccesskey
  service_plan_id               = var.aspId
  public_network_access_enabled = var.publicNetworkAccessEnabled
  tags                          = var.tags
  virtual_network_subnet_id     = var.virtualNetworkSubnetId
  # app_settings = {
  #   # //"FUNCTIONS_WORKER_RUNTIME"                 = var.functionsWorkerRuntime
  #   # //"WEBSITE_RUN_FROM_PACKAGE"                 = var.websiteRunFromPackage
  #   "APPINSIGHTS_INSTRUMENTATIONKEY"           = var.appinsightInstrumentationkey
  #   "AzureWebJobsStorage"                      = var.storageConnectionString
  #   "FUNCTIONS_EXTENSION_VERSION"              = var.functionExtensionVersion
  #   "FUNCTIONS_WORKER_RUNTIME"                 = var.functionsWorkerRuntime
  #   "WEBSITE_CONTENTAZUREFILECONNECTIONSTRING" = var.storageConnectionString
  #   "WEBSITE_CONTENTSHARE"                     = var.storageShareName
  #   "WEBSITE_USE_PLACEHOLDER_DOTNETISOLATED"   = var.WebsiteUsePlaceholderDotnetIsolated
  # }
  site_config {
    always_on                = var.alwaysOn
    vnet_route_all_enabled   = var.vnetRouteAllEnabled
    use_32_bit_worker        = var.use32BitWorker
    elastic_instance_minimum = var.elasticInstanceMinimum
    dynamic "application_stack" {
      for_each = var.dockerStack == {} ? [] : [null]
      content {
        docker {
          registry_url = var.dockerStack.registryUrl
          image_name   = var.dockerStack.imageName
          image_tag    = var.dockerStack.imageTag
        }
      }
    }
    # dynamic "application_stack" {
    #   for_each = var.dotnetStack == {} ? [] : [null]
    #   content {
    #     dotnet_version = var.dotnetStack.dotnetVersion
    #   }
    # }
  }
  identity { type = var.identityType }
  lifecycle { ignore_changes = [app_settings, site_config, sticky_settings, tags] }
}
