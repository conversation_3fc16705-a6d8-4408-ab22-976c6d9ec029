// REQUIRED VARIABLES (variables which are needed to be passed)
variable "firewallPolicyName" {
  description = "Name of the Firewall Policy"
  type        = string
}
variable "rgName" {
  description = "Name of the Resource Group for the Firewall Policy"
  type        = string
}
variable "location" {
  description = "Location for the Firewall Policy"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}