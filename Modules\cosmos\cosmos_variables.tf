// REQUIRED VARIABLES
variable "location" {
  description = "Azure region where the Cosmos DB will be created"
  type        = string
}

variable "rgName" {
  description = "Name of the resource group where the Cosmos DB will be created"
  type        = string
}

variable "cosmosSettings" {
  description = "Core settings for the Cosmos DB account"
  type = object({
    accountName = string
    kind        = string
    offerType   = string
  })
  validation {
    condition     = contains(["GlobalDocumentDB", "MongoDB", "Parse"], var.cosmosSettings.kind)
    error_message = "The kind must be one of: GlobalDocumentDB, MongoDB, or Parse."
  }
  validation {
    condition     = var.cosmosSettings.offerType == "Standard"
    error_message = "The offer_type must be Standard."
  }
}

variable "geoLocations" {
  description = "List of geo-locations for the Cosmos DB account"
  type = list(object({
    location          = string
    failoverPriority = number
    zoneRedundant    = optional(bool, false)
  }))
}

variable "consistencyPolicy" {
  description = "Consistency policy for the Cosmos DB account"
  type = object({
    level               = string
    maxIntervalInSeconds = optional(number, 5)
    maxStalenessPrefix  = optional(number, 100)
  })
  validation {
    condition     = contains(["BoundedStaleness", "Eventual", "Session", "Strong", "ConsistentPrefix"], var.consistencyPolicy.level)
    error_message = "The consistency level must be one of: BoundedStaleness, Eventual, Session, Strong, or ConsistentPrefix."
  }
}

// OPTIONAL VARIABLES
variable "cosmosConfig" {
  description = "Configuration options for the Cosmos DB account"
  type = object({
    analyticalStorageEnabled    = optional(bool, false)
    publicNetworkAccessEnabled = optional(bool, true)
    # enableAutomaticFailover     = optional(bool, true)
    # enableFreeTier              = optional(bool, false)
  })
  default = {}
}

variable "capabilities" {
  description = "List of capabilities to enable for the Cosmos DB account"
  type        = list(string)
  default     = []
}

variable "backupPolicy" {
  description = "Backup policy for the Cosmos DB account"
  type = object({
    type                = string
    intervalInMinutes  = optional(number)
    retentionInHours   = optional(number)
    storageRedundancy  = optional(string)
  })
  default = null
}

variable "virtualNetworkRules" {
  description = "List of virtual network rules for the Cosmos DB account"
  type = list(object({
    id                                   = string
    ignoreMissingVnetServiceEndpoint    = optional(bool, false)
  }))
  default = []
}

variable "corsRules" {
  description = "CORS rules for the Cosmos DB account"
  type = list(object({
    allowedHeaders    = list(string)
    allowedMethods    = list(string)
    allowedOrigins    = list(string)
    exposedHeaders    = list(string)
    maxAgeInSeconds   = number
  }))
  default = []
}

variable "sqlDatabases" {
  description = "Map of SQL databases to create"
  type = map(object({
    throughput     = optional(number)
    maxThroughput = optional(number)
  }))
  default = {}
}

variable "sqlContainers" {
  description = "Map of SQL containers to create"
  type = map(object({
    name                = string
    databaseName       = string
    partitionKeyPath   = string
    partitionKeyVersion = optional(number)
    throughput          = optional(number)
    maxThroughput      = optional(number)
    indexingPolicy     = optional(object({
    indexingMode     = string
    includedPaths    = optional(list(string))
    excludedPaths    = optional(list(string))
    }))
    uniqueKeys         = optional(list(list(string)))
  }))
  default = {}
}

variable "tags" {
  description = "Tags to be applied to the Cosmos DB account"
  type        = map(string)
  default     = {}
}