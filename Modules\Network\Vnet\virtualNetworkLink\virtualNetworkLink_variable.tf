// REQUIRED VARIABLES (variables which are needed to be passed)
variable "privateDnsLinkName" {
  description = "Name of the Private DNS zone virtual network link"
  type        = string
}
variable "privateDnsLinkRegistrationEnabled" {
  description = "Boolean flag indicating whether registration is enabled for the Private DNS zone virtual network link"
  type        = bool
}
variable "rgName" {
  description = "Name of the resource group containing the Private DNS zone"
  type        = string
}
variable "privateDnsLinkZoneName" {
  description = "Name of the Private DNS zone"
  type        = string
}
variable "privateDnsLinkVirtualNetworkId" {
  description = "ID of the virtual network to link with the Private DNS zone"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
