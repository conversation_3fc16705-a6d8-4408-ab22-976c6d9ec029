resource "azurerm_application_gateway" "applicationGateway" {
  name                = var.applicationGatewayName
  resource_group_name = var.rgName
  location            = var.location
  tags                = var.apimTags
  sku {
    name     = var.sku
    tier     = var.tier
    capacity = var.capacity
  }
  gateway_ip_configuration {
    name      = var.gatewayIpConfiguration
    subnet_id = var.subnetId
  }
  frontend_port {
    name = var.frontendPortName
    port = var.port
  }
  frontend_ip_configuration {
    name                 = var.frontendIpConfigurationName
    public_ip_address_id = var.publicIpId
  }
  backend_address_pool {
    name = var.backendAddressPoolName
  }
  backend_http_settings {
    name                  = var.httpSettingName
    cookie_based_affinity = var.cookieBasedAffinity
    path                  = var.path
    port                  = var.port
    protocol              = var.protocol
    request_timeout       = var.requestTimeout
  }
  http_listener {
    name                           = var.listenerName
    frontend_ip_configuration_name = var.frontendIpConfigurationName
    frontend_port_name             = var.frontendPortName
    protocol                       = var.protocol
  }
  request_routing_rule {
    name                       = var.requestRoutingRuleName
    priority                   = var.priority
    rule_type                  = var.ruleType
    http_listener_name         = var.listenerName
    backend_address_pool_name  = var.backendAddressPoolName
    backend_http_settings_name = var.httpSettingName
  }
}