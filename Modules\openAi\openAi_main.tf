resource "azurerm_cognitive_account" "openAi" {
  name                          = var.openAiName
  location                      = var.location
  resource_group_name           = var.rgName
  kind                          = var.openAiKind
  sku_name                      = var.openAiSkuName
  tags                          = merge(var.tags, { "Region" : var.location })
  public_network_access_enabled = var.publiceNetworkAccessEnabled
  custom_subdomain_name         = var.oaCustomSubdomain
  identity {
    type = var.identityType
  }
  lifecycle {ignore_changes = [network_acls]}
}

resource "azurerm_cognitive_deployment" "openAiDeployment" {
  for_each             = var.oaiModel
  name                 = each.value.oaDeploymentName
  cognitive_account_id = azurerm_cognitive_account.openAi.id
  sku {
    name = each.value.oaDeloymentSkuName
  }
  model {
    format  = each.value.modelFormat
    name    = each.value.modelName
    version = each.value.modelVersion
  }
  lifecycle {ignore_changes = [rai_policy_name]}
}
