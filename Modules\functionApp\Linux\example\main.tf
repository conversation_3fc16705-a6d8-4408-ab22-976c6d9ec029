// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "functionAppLinux" {
  source                     = "../" // Modifiy module path accordingly 
  faName                     = "DemoFunctionAppLinux"
  rgName                     = "Demo-RG"
  location                   = "Central India"
  storageAccountName         = "demostorageaccount"
  storageAccountaccesskey    = "" // Associate Storage Account Access Key on this Place with the help of either Module (using Output), Locals or Resource Block
  aspId                      = "" // Associate App Service Plan ID on this Place with the help of either Module (using Output), Locals or Resource Block
  publicNetworkAccessEnabled = true
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  virtualNetworkSubnetId = "" // Associate Subnet ID on this Place with the help of either Module (using Output), Locals or Resource Block
  alwaysOn               = true
  vnetRouteAllEnabled    = true
  use32BitWorker         = true
  elasticInstanceMinimum = 2
  dockerStack = {
    registryUrl = "demodockerhub.com"
    imageName   = "myfunctionappimage"
    imageTag    = "v1.0"
  }
  # dotnetStack = {
  #   dotnetVersion = "v3.0"
  # }
  identityType = "SystemAssigned"
}