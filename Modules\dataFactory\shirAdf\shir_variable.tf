// REQUIRED VARIABLES (variables which are needed to be passed)
variable "selfHostedIRName" {
  description = "The name of the Data Factory"
  type        = string
}
variable "dataFactoryId" {
  description = "The Data Factory ID which will be associated with the Linked Service"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "rbacAuthorization" {
  description = "RBAC authorization settings for Azure Data Factory"
  type = map(object({
    resourceId = string
  }))
}