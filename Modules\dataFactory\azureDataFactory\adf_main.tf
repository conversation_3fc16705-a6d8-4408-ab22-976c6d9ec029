# This block configures the basic settings for an Azure Data Factory, including its name, location, and resource group.
resource "azurerm_data_factory" "dataFactory" {
  name                   = var.dataFactoryName
  location               = var.location
  resource_group_name    = var.rgName
  public_network_enabled = var.publicNetworkEnabled

  # This dynamic block configures GitHub integration settings for the Azure Data Factory using multiple GitHub configurations.
  dynamic "github_configuration" {
    for_each = var.githubConfiguration
    content {
      account_name       = github_configuration.value.accountName
      branch_name        = github_configuration.value.branchName
      git_url            = github_configuration.value.gitUrl
      repository_name    = github_configuration.value.repositoryName
      root_folder        = github_configuration.value.rootFolder
      publishing_enabled = github_configuration.value.publishingEnabled
    }
  }

  # This dynamic block configures global parameters for the Azure Data Factory using multiple global parameter configurations.
  dynamic "global_parameter" {
    for_each = var.globalParameter
    content {
      name  = global_parameter.value.name
      value = global_parameter.value.value
      type  = global_parameter.value.type
    }
  }

  # This block configures the identity settings for the Azure resource, specifying the type of identity and associated identity IDs.
  identity {
    type         = var.identityType
    identity_ids = toset(var.identityIds)
  }
  customer_managed_key_id          = var.CustomerManagedKeyId
  customer_managed_key_identity_id = var.CustomerManagedKeyIdentityId

  # This block specifies lifecycle settings for AKS, ignoring changes based on the provided lifecycle configuration.
  # lifecycle {
  #   ignore_changes = [var.lifecycle_ignore_changes]
  # }
}
