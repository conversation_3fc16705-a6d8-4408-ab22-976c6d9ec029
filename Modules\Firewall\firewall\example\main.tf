// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "firewall" {
  source       = "../" // Modifiy module path accordingly 
  firewallName = "Demo-Firewall"
  location     = "Central India"
  rgName       = "Demo-RG"
  skuName      = "AZFW_VNet"
  skuTier      = "Standard"
  zones        = ["1"]
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  ipConfigurationName = "firewall-ip-config"
  subnetId            = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/virtualNetworks/demo-vnet/subnets/AzureFirewallSubnet" // Associate Subnet Id on this Place with the help of either Module (using Output), Locals or Resource Block
  publicIpId          = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/publicIPAddresses/firewall-pip"                        // Associate Public IP Id on this Place with the help of either Module (using Output), Locals or Resource Block
}