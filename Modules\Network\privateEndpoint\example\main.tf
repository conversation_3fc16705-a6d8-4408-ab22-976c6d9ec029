// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "privateEndpoint" {
  source                            = "../" // Modifiy module path accordingly 
  privateEndpointName               = "DemoEndpoint"
  location                          = "Central India"
  rgName                            = "Demo-RG"
  subnetEndpointId                  = "" // Associate Subnet Id on this Place with the help of either Module (using Output), Locals or Resource Block  
  privateServiceConnectionName      = "DemoServiceConnection"
  privateConnectionResourceId       = ""            // Associate Target Resource Id on this Place with the help of either Module (using Output), Locals or Resource Block
  privateConnectionSubresourceNames = ["sqlServer"] // Specify the subresource based on the target service type
  isManualConnection                = false
  privateDnsZoneGroupName           = "DemoPrivateDnsZoneGroup"
  privateDnsZoneIds                 = [""] // Associate Private DNS Zone Ids on this Place with the help of either Module (using Output), Locals or Resource Block
}