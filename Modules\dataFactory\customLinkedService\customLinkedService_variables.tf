// REQUIRED VARIABLES (variables which are needed to be passed)
variable "linkedServiceName" {
  description = "The name of the linked service"
  type        = string
}
variable "dataFactoryId" {
  description = "The ID of the Azure Data Factory where the linked service will be created"
  type        = string
}
variable "linkedServiceType" {
  description = "The type of linked service"
  type        = string
}
variable "typeProperties<PERSON>son" {
  description = "The JSON string that contains the type properties for the linked service"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "linkedServiceDescription" {
  description = "The description of the linked service"
  type        = string
}
variable "parameters" {
  description = "Additional parameters for the linked service"
  type        = map(string)
}
variable "annotations" {
  description = "Annotations for the linked service"
  type        = list(string)
}
variable "integrationRuntime" {
  description = "Integration runtime settings for the linked service"
  type = map(object({
    name = string
  }))
}