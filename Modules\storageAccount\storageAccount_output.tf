output "storageAccountName" {
  description = "The name of the Storage Account"
  value       = azurerm_storage_account.storageAccount.name
}
output "storageAccountId" {
  description = "The ID of the Storage Account"
  value       = azurerm_storage_account.storageAccount.id
}
output "storageAccountaccesskey" {
  description = "The primary access key of the Azure Storage Account"
  value       = azurerm_storage_account.storageAccount.primary_access_key
}
output "storageConnectionString" {
  description = "The primary connection string of the Azure Storage Account"
  value       = azurerm_storage_account.storageAccount.primary_connection_string
}
output "storagePrimaryBlobHost" {
  description = "The primary blob endpoint hostname of the Azure Storage Account"
  value       = azurerm_storage_account.storageAccount.primary_blob_host
}