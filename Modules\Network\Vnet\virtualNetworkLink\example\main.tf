// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "virtualNetworkLink" {
  source                            = "../" // Modifiy module path accordingly 
  privateDnsLinkName                = "DemoLink"
  privateDnsLinkRegistrationEnabled = true
  rgName                            = "Demo-RG"
  privateDnsLinkZoneName            = "DemoLinkZone"
  privateDnsLinkVirtualNetworkId    = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/virtualNetworks/demo-vnet" // Associate Virtual Network Id on this Place with the help of either Module (using Output), Locals or Resource Block
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
}