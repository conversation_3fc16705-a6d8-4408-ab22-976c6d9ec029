resource "azurerm_storage_account" "storageAccount" {
  name                          = var.storageAccountName
  resource_group_name           = var.rgName
  location                      = var.location
  account_tier                  = var.accountTier
  account_replication_type      = var.accountReplicationType
  account_kind                  = var.accountKind
  https_traffic_only_enabled    = var.enableHttpsTrafficOnly
  min_tls_version               = var.minTlsVersion
  shared_access_key_enabled     = var.sharedAccessKeyEnabled
  is_hns_enabled                = var.isHnsEnabled
  public_network_access_enabled = var.publicNetworkAccessEnabled
  tags                          = var.tags
  dynamic "network_rules" {
    for_each = var.networkRules == {} ? [] : ["Deny"]
    content {
      default_action = var.networkRules.defaultAction
    }
  }
  lifecycle {
    ignore_changes = [
      network_rules, public_network_access_enabled
    ]
  }
}