// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "subnets" {
  source                                   = "../" // Modifiy module path accordingly 
  subnetName                               = "Demo-Subnet"
  rgName                                   = "Demo-RG"
  subnetAddressPrefixes                    = ["********/24"]
  virtualNetworkName                       = "Demo-Vnet" // Associate Virtual Network Name on this Place with the help of either Module (using Output), Locals or Resource Block
  privateLinkServiceNetworkPoliciesEnabled = true
  subnetDelegations = {
    subnetDelegationName  = "appservice-delegation"
    serviceDelegationName = "Microsoft.Web/serverFarms"
    actions               = "Microsoft.Network/virtualNetworks/subnets/action"
  }
  subnetNsgAssociation = true
  nsgId                = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/networkSecurityGroups/demo-nsg" // Associate Network Security Group Id on this Place with the help of either <PERSON>dule (using Output), Locals or Resource Block
  subnetRtAssociation  = true
  rtId                 = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/routeTables/demo-rt" // Associate Route Table Id on this Place with the help of either Module (using Output), Locals or Resource Block
}