// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "adfLinkedAdls" {
  source                                  = "../" // Modifiy module path accordingly 
  linkedServiceAdlsName                   = "DemoADLS"
  linkedServiceAdlsDataFactoryId          = ""  // Associate Data Factory ID on this Place with the help of either Module (using Output), Locals or Resource Block
  linkedServiceAdlsDescription            = "This is a demo ADLS linked service"
  linkedServiceAdlsIntegrationRuntimeName = "DemoIntegrationRuntime"
  linkedServiceAdlsUrl                    = "https://your_adls_url.com"
  linkedServiceAdlsUmi                    = false
}