resource "azurerm_data_factory_linked_service_sftp" "adfLinkedSftp" {
  name                     = var.linkedServiceSftpName
  data_factory_id          = var.linkedServiceSftpDataFactoryId
  description              = var.linkedServiceSftpDescription
  skip_host_key_validation = var.linkedServiceSftpSkipHostKeyValidation
  host_key_fingerprint     = var.linkedServiceSftpHostKeyFingerprint
  authentication_type      = var.linkedServiceSftpAuthenticationType
  host                     = var.linkedServiceSftpHost
  port                     = var.linkedServiceSftpPort
  username                 = var.linkedServiceSftpUsername
  password                 = var.linkedServiceSftpPassword
  integration_runtime_name = var.linkedServiceSftpIntegrationRuntimeName
}