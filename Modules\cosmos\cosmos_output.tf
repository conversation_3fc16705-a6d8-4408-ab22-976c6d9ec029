output "cosmosDbId" {
  description = "The ID of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.id
}

output "cosmosDbName" {
  description = "The name of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.name
}

output "cosmosDbEndpoint" {
  description = "The endpoint of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.endpoint
}

output "cosmosDbReadEndpoints" {
  description = "The read endpoints of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.read_endpoints
}

output "cosmosDbWriteEndpoints" {
  description = "The write endpoints of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.write_endpoints
}

output "cosmosDbPrimaryMasterKey" {
  description = "The primary master key of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.primary_key
  sensitive   = true
}

output "cosmosDbSecondaryMasterKey" {
  description = "The secondary master key of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.secondary_key
  sensitive   = true
}

output "cosmosDbPrimaryReadonlyMasterKey" {
  description = "The primary read-only master key of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.primary_readonly_key
  sensitive   = true
}

output "cosmosDbSecondaryReadonlyMasterKey" {
  description = "The secondary read-only master key of the Cosmos DB account"
  value       = azurerm_cosmosdb_account.cosmosDb.secondary_readonly_key
  sensitive   = true
}

