// REQUIRED VARIABLES (variables which are needed to be passed)
variable "virtualNetworkName" {
  description = "The name for the new Azure Virtual Network"
  type        = string
}
variable "location" {
  description = "The location (region) where the Azure Virtual Network will be created"
  type        = string
}
variable "rgName" {
  description = "The name of the Azure Resource Group where the Virtual Network will be created"
  type        = string
}
variable "virtualNetworkAddressSpace" {
  description = "The address space of the Azure Virtual Network"
  type        = list(string)
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}