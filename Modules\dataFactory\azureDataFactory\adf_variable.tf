// REQUIRED VARIABLES (variables which are needed to be passed)
variable "dataFactoryName" {
  description = "Specifies the name of the Data Factory"
  type        = string
}
variable "location" {
  description = "Specifies the supported Azure location where the resource exists"
  type        = string
}
variable "rgName" {
  description = "The name of the resource group in which the Data Factory is created"
  type        = string
}
variable "identityType" {
  description = "Specifies the type of Managed Service Identity that should be configured on the Data Factory"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
# variable "lifecycle_ignore_changes" {
#   description = "This block specifies lifecycle settings for AKS, ignoring changes based on the provided lifecycle configuration."
#   type        = string
# }
variable "identityIds" {
  description = "Specifies a list of User Assigned Managed Identity IDs to be assigned to this Data Factory"
  type        = list(string)
}
variable "CustomerManagedKeyId" {
  description = "Specifies the Azure Key Vault Key ID to be used as the Customer Managed Key (CMK) for double encryption"
  type        = string
  nullable    = true
}
variable "CustomerManagedKeyIdentityId" {
  description = "Specifies the ID of the user assigned identity associated with the Customer Managed Key."
  type        = string
  nullable    = true
}
variable "githubConfiguration" {
  description = "Map of GitHub configurations"
  type = map(object({
    accountName       = string
    branchName        = string
    gitUrl            = optional(string, null)
    repositoryName    = string
    rootFolder        = string
    publishingEnabled = optional(bool, null)
  }))
}
variable "globalParameter" {
  description = "Map of global parameters"
  type = map(object({
    name  = string
    value = string
    type  = string
  }))
}
variable "publicNetworkEnabled" {
  description = "Defines whether the Public Network is enabled or not"
  type        = bool
}