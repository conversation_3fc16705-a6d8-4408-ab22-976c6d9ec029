// REQUIRED VARIABLES (variables which are needed to be passed)
variable "applicationGatewayName" {
  description = "The name of the Application Gateway"
  type        = string
}
variable "rgName" {
  description = "Specifies the Resource Group where the Application Gateway is present"
  type        = string
}
variable "location" {
  description = "The location of the Application Gateway"
  type        = string
}
variable "sku" {
  description = "SKU Tier that should be used for Application Gateway"
  type        = string
}
variable "tier" {
  description = "The tier of the Application Gateway"
  type        = string
}
variable "gatewayIpConfiguration" {
  description = "The name of the gateway IP configuration"
  type        = string
}
variable "subnetId" {
  description = "The ID of the subnet"
  type        = string
}
variable "frontendPortName" {
  description = "The name of the frontend port"
  type        = string
}
variable "port" {
  description = "The port number for the frontend port"
  type        = number
}
variable "frontendIpConfigurationName" {
  description = "Frontend IP Configuration"
  type        = string
}
variable "backendAddressPoolName" {
  description = "The name of the backend address pool"
  type        = string
}
variable "httpSettingName" {
  description = "The name of the backend HTTP settings"
  type        = string
}
variable "cookieBasedAffinity" {
  description = "Cookie based affinity setting"
  type        = string
}
variable "protocol" {
  description = "Protocol for the frontend and backend HTTP settings"
  type        = string
}
variable "listenerName" {
  description = "The name of the HTTP listener"
  type        = string
}
variable "requestRoutingRuleName" {
  description = "The name of the request routing rule"
  type        = string
}
variable "ruleType" {
  description = "Type of the request routing rule"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "capacity" {
  description = "The capacity of the Application Gateway"
  type        = number
}
variable "publicIpId" {
  description = "The ID of the public IP address"
  type        = string
}
variable "path" {
  description = "Path for the backend HTTP settings"
  type        = string
}
variable "requestTimeout" {
  description = "Request timeout for the backend HTTP settings"
  type        = number
}
variable "priority" {
  description = "Priority of the request routing rule"
  type        = number
}
variable "apimTags" {
  type = map(string)
}