# Creates a virtual network link for an Azure Private DNS zone, enabling registration and associating it with a specified virtual network.
resource "azurerm_private_dns_zone_virtual_network_link" "virtualNetworkLink" {
  name                  = var.privateDnsLinkName
  registration_enabled  = var.privateDnsLinkRegistrationEnabled
  resource_group_name   = var.rgName
  private_dns_zone_name = var.privateDnsLinkZoneName
  virtual_network_id    = var.privateDnsLinkVirtualNetworkId
  tags                  = var.tags
}
