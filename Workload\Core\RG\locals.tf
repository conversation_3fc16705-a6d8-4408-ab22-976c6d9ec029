# Data sources to reference existing network infrastructure
data "azurerm_resource_group" "hub_rg" {
  name = "kartik.jindal_RG"
}

data "azurerm_virtual_network" "hub_vnet" {
  name                = "Hub-VNet"
  resource_group_name = data.azurerm_resource_group.hub_rg.name
}

data "azurerm_subnet" "hub_firewall_subnet" {
  name                 = "AzureFirewallSubnet"
  virtual_network_name = data.azurerm_virtual_network.hub_vnet.name
  resource_group_name  = data.azurerm_resource_group.hub_rg.name
}

data "azurerm_subnet" "hub_bastion_subnet" {
  name                 = "AzureBastionSubnet"
  virtual_network_name = data.azurerm_virtual_network.hub_vnet.name
  resource_group_name  = data.azurerm_resource_group.hub_rg.name
}

data "azurerm_subnet" "hub_normal_subnet" {
  name                 = "Hub-Normal-Subnet"
  virtual_network_name = data.azurerm_virtual_network.hub_vnet.name
  resource_group_name  = data.azurerm_resource_group.hub_rg.name
}

data "azurerm_public_ip" "firewall_pip" {
  name                = "Firewall-PublicIP"
  resource_group_name = data.azurerm_resource_group.hub_rg.name
}

data "azurerm_public_ip" "bastion_pip" {
  name                = "Bastion-PublicIP"
  resource_group_name = data.azurerm_resource_group.hub_rg.name
}

# Local values for resource organization
locals {
  # Common tags for all resources
  common_tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
    ManagedBy   = "Terraform"
    Project     = "Hub-Infrastructure"
  }

  # Hub resource group name
  hub_rg_name = data.azurerm_resource_group.hub_rg.name

  # Hub VNet details
  hub_vnet_id   = data.azurerm_virtual_network.hub_vnet.id
  hub_vnet_name = data.azurerm_virtual_network.hub_vnet.name

  # Location
  location = data.azurerm_resource_group.hub_rg.location

  # Private DNS zone names for Azure services
  private_dns_zones = {
    acr           = "privatelink.azurecr.io"
    aml           = "privatelink.api.azureml.ms"
    keyvault      = "privatelink.vaultcore.azure.net"
    aisearch      = "privatelink.search.windows.net"
    cosmosdb      = "privatelink.documents.azure.com"
    aks           = "privatelink.centralindia.azmk8s.io"
    storage       = "privatelink.blob.core.windows.net"
    adf           = "privatelink.datafactory.azure.net"
  }
}