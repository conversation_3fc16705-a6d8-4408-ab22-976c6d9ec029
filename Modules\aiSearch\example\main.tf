// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "aiSearch" {
  source                     = "../" // Modifiy module path accordingly 
  aiSearchName               = "AISearch"
  aiSearchRgName             = "Demo-RG"
  aiSearchRgLocation         = "Central India"
  aiSearchSku                = "basic"
  publicNetworkAccessEnabled = true
  aiSearchTag = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  aiServiceHostingMode = "default"
  aiPartitionCount     = 2
  aiReplicaCount       = 1
}