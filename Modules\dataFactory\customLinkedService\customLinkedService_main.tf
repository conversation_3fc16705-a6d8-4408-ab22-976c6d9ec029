resource "azurerm_data_factory_linked_custom_service" "customLinkedService" {
  name                 = var.linkedServiceName
  data_factory_id      = var.dataFactoryId
  type                 = var.linkedServiceType
  description          = var.linkedServiceDescription
  type_properties_json = var.typePropertiesJson
  parameters           = var.parameters
  annotations          = var.annotations
  # This dynamic block configures integration runtimes for the Azure Data Factory, allowing multiple integration runtimes to be specified, with each containing a name.
  dynamic "integration_runtime" {
    for_each = var.integrationRuntime
    content {
      name = integration_runtime.value.name
    }
  }
}
