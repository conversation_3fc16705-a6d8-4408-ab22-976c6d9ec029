// REQUIRED VARIABLES (variables which are needed to be passed)
variable "linkedServiceSftpName" {
  description = "The name of the SFTP linked service"
  type        = string
}
variable "linkedServiceSftpDataFactoryId" {
  description = "The ID of the data factory associated with the SFTP linked service"
  type        = string
}
variable "linkedServiceSftpAuthenticationType" {
  description = "The authentication type used for the SFTP linked service"
  type        = string
}
variable "linkedServiceSftpHost" {
  description = "The host address of the SFTP server"
  type        = string
}
variable "linkedServiceSftpPort" {
  description = "The port number of the SFTP server"
  type        = number
}
variable "linkedServiceSftpUsername" {
  description = "The username for authentication to the SFTP server"
  type        = string
}
variable "linkedServiceSftpPassword" {
  description = "The password for authentication to the SFTP server"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "linkedServiceSftpDescription" {
  description = "The description of the SFTP linked service"
  type        = string
}
variable "linkedServiceSftpSkipHostKeyValidation" {
  description = "Indicates whether to skip host key validation for the SFTP linked service or not"
  type        = string
}
variable "linkedServiceSftpHostKeyFingerprint" {
  description = "The fingerprint of the host key for the SFTP linked service"
  type        = string
}
variable "linkedServiceSftpIntegrationRuntimeName" {
  description = "The name of the integration runtime associated with the SFTP linked service"
  type        = string
}


