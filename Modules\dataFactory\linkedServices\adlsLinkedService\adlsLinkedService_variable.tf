// REQUIRED VARIABLES (variables which are needed to be passed)
variable "linkedServiceAdlsName" {
  description = "The name of the linked service to Azure Data Lake Storage Gen2"
  type        = string
}
variable "linkedServiceAdlsDataFactoryId" {
  description = "The ID of the data factory associated with the linked service to Azure Data Lake Storage Gen2"
  type        = string
}
variable "linkedServiceAdlsUrl" {
  description = "The URL of the Azure Data Lake Storage Gen2 account"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "linkedServiceAdlsDescription" {
  description = "The description of the linked service to Azure Data Lake Storage Gen2"
  type        = string
}
variable "linkedServiceAdlsIntegrationRuntimeName" {
  description = "The name of the integration runtime associated with the linked service to Azure Data Lake Storage Gen2"
  type        = string
}
variable "linkedServiceAdlsUmi" {
  description = "Indicates whether to use managed identity for authentication or not"
  type        = bool
}
