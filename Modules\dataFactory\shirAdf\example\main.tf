// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "shirAdf" {
  source           = "../" // Modifiy module path accordingly 
  selfHostedIRName = "DemoSHIR"
  dataFactoryId    = "" // Associate Data Factory Id on this Place with the help of either Module (using Output), Locals or Resource Block
  rbacAuthorization = {
    "Demo-Auth" = {
      resourceId = "" // Associate Resource Id on this Place with the help of either Module (using Output), Locals or Resource Block
    }
  }
}