// REQUIRED VARIABLES (variables which are needed to be passed)
variable "firewallName" {
  type        = string
  description = "Name of the Firewall"
}
variable "location" {
  description = "Location for the Firewall"
  type        = string
}
variable "rgName" {
  description = "Name of the Resource Group for the Firewall"
  type        = string
}
variable "skuName" {
  description = "SKU Name for the Firewall, specifying the performance level"
  type        = string
}
variable "skuTier" {
  description = "SKU Tier for the Firewall, specifying the tier of the SKU"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "zones" {
  description = "Availability Zones in which the Firewall will be deployed"
  type        = list(string)
}
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "ipConfigurationName" {
  description = "Name of the IP configuration for the Firewall"
  type        = string
}
variable "subnetId" {
  description = "Name of the IP configuration for the Firewall"
  type        = string
}
variable "publicIpId" {
  description = "The ID of the Public IP address to associate with the Firewall"
  type        = string
}