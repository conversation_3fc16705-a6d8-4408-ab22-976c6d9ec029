// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "openAi" {
  source                      = "../" // Modifiy module path accordingly 
  openAiName                  = "Demo-OpenAi"
  location                    = "Central India"
  rgName                      = "Demo-RG"
  openAiKind                  = "OpenAI"
  openAiSkuName               = "P1"
  publiceNetworkAccessEnabled = true
  oaCustomSubdomain           = "Demo-CustomSubDomain"
  identityType                = "SystemAssigned"
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  oaiModel = {
    "Demo-OpenAiModel" = {
      oaDeploymentName   = "Demo-OpenAiModel"
      oaDeloymentSkuName = "Standard"
      modelFormat        = "OpenAI"
      modelName          = "gpt-35-turbo"
      modelVersion       = "0301"
    }
  }
}