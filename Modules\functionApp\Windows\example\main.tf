// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "functionAppWindows" {
  source                     = "../" // Modifiy module path accordingly 
  faName                     = "DemoFunctionAppWindows"
  rgName                     = "Demo-RG"
  location                   = "Central India"
  storageAccountName         = "demostorageaccount"
  storageAccountaccesskey    = "Eby8vdM02xNOcqFlqUwJPLlmEtlCDXJ1OUzFT50uSRZ6IFsuFq2UVErCz4I6tq/K1SZFPTOtr/KBHBeksoGMGw=="                                                            // Associate Storage Account Access Key on this Place with the help of either Module (using Output), Locals or Resource Block
  aspId                      = "/subscriptions/d4cdcc9b-a5dd-4e67-b0cd-bb10e7bb7f96/resourceGroups/kartikeya-it-infra/providers/Microsoft.Web/serverFarms/ASP-kartikeyaitinfra-96d3" // Associate App Service Plan ID on this Place with the help of either Module (using Output), Locals or Resource Block
  publicNetworkAccessEnabled = true
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  virtualNetworkSubnetId = "/subscriptions/your_subscription_id/resourceGroups/myresourcegroup/providers/Microsoft.Network/virtualNetworks/myvirtualnetwork/subnets/mysubnet" // Associate Subnet ID on this Place with the help of either Module (using Output), Locals or Resource Block
  alwaysOn               = true
  vnetRouteAllEnabled    = true
  use32BitWorker         = true
  elasticInstanceMinimum = 2
  dotnetStack = {
    dotnetVersion = "v3.0"
  }
  identityType = "SystemAssigned"
}