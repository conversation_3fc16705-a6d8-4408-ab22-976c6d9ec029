# Azure Firewall Outputs
output "firewall_ids" {
  description = "Map of Azure Firewall IDs"
  value = {
    for k, v in module.azure_firewalls : k => v.firewallId
  }
}

output "firewall_names" {
  description = "Map of Azure Firewall names"
  value = {
    for k, v in module.azure_firewalls : k => v.firewallName
  }
}

# Note: Firewall private IP is not exposed by the module

# Azure Bastion Outputs
output "bastion_ids" {
  description = "Map of Azure Bastion IDs"
  value = {
    for k, v in module.azure_bastions : k => v.bastionId
  }
}

output "bastion_names" {
  description = "Map of Azure Bastion names"
  value = {
    for k, v in module.azure_bastions : k => v.bastionName
  }
}

output "bastion_dns_names" {
  description = "Map of Azure Bastion DNS names"
  value = {
    for k, v in module.azure_bastions : k => v.bastionDnsName
  }
}

# Application Gateway Outputs
output "app_gateway_ids" {
  description = "Map of Application Gateway IDs"
  value = {
    for k, v in module.application_gateways : k => v.applicationGatewayId
  }
}

output "app_gateway_names" {
  description = "Map of Application Gateway names"
  value = {
    for k, v in module.application_gateways : k => v.applicationGatewayName
  }
}

output "app_gateway_public_ip_ids" {
  description = "Map of Application Gateway Public IP IDs"
  value = {
    for k, v in module.app_gateway_public_ips : k => v.publicIpId
  }
}

# Private DNS Zone Outputs
output "private_dns_zone_ids" {
  description = "Map of Private DNS Zone IDs"
  value = {
    for k, v in module.private_dns_zones : k => v.privateDnsZoneId
  }
}

output "private_dns_zone_names" {
  description = "Map of Private DNS Zone names"
  value = {
    for k, v in module.private_dns_zones : k => v.privateDnsZoneName
  }
}

# VNet Link Outputs
output "vnet_link_ids" {
  description = "Map of VNet Link IDs"
  value = {
    for k, v in module.vnet_links : k => v.virtualNetworkLinkId
  }
}

output "vnet_link_names" {
  description = "Map of VNet Link names"
  value = {
    for k, v in module.vnet_links : k => v.virtualNetworkLinkName
  }
}

# Hub Infrastructure Summary
output "hub_infrastructure_summary" {
  description = "Summary of deployed hub infrastructure"
  value = {
    resource_group_name = local.hub_rg_name
    location           = local.location
    hub_vnet_id        = local.hub_vnet_id
    hub_vnet_name      = local.hub_vnet_name
    firewall_count     = length(module.azure_firewalls)
    bastion_count      = length(module.azure_bastions)
    app_gateway_count  = length(module.application_gateways)
    dns_zone_count     = length(module.private_dns_zones)
    vnet_link_count    = length(module.vnet_links)
  }
}

# Specific Resource Outputs for Easy Reference
output "hub_firewall_id" {
  description = "Hub Firewall ID"
  value       = length(module.azure_firewalls) > 0 ? module.azure_firewalls["hub_firewall"].firewallId : null
}

output "hub_bastion_id" {
  description = "Hub Bastion ID"
  value       = length(module.azure_bastions) > 0 ? module.azure_bastions["hub_bastion"].bastionId : null
}

output "hub_app_gateway_id" {
  description = "Hub Application Gateway ID"
  value       = length(module.application_gateways) > 0 ? module.application_gateways["hub_app_gateway"].applicationGatewayId : null
}

# Private DNS Zone IDs for specific services (useful for private endpoints)
output "acr_dns_zone_id" {
  description = "ACR Private DNS Zone ID"
  value       = module.private_dns_zones["acr_dns_zone"].privateDnsZoneId
}

output "aml_dns_zone_id" {
  description = "AML Private DNS Zone ID"
  value       = module.private_dns_zones["aml_dns_zone"].privateDnsZoneId
}

output "keyvault_dns_zone_id" {
  description = "Key Vault Private DNS Zone ID"
  value       = module.private_dns_zones["keyvault_dns_zone"].privateDnsZoneId
}

output "storage_dns_zone_id" {
  description = "Storage Account Private DNS Zone ID"
  value       = module.private_dns_zones["storage_dns_zone"].privateDnsZoneId
}
