// REQUIRED VARIABLES (variables which are needed to be passed)
variable "publicIpName" {
  description = "The name of the Public IP"
  type        = string
}
variable "location" {
  description = "The region where the Public IP will be created"
  type        = string
}
variable "rgName" {
  description = "The name of the resource group where the Public IP will be created"
  type        = string
}
variable "allocationMethod" {
  description = "The allocation method for the Public IP (Static or Dynamic)"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "publicIpSKU" {
  description = "The SKU for the Public IP"
  type        = string
}
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "zones" {
  description = "The availability zones for the Public IP"
  type        = list(string)
}