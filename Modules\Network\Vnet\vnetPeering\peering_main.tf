# Creates a peering connection between two Azure virtual networks.
resource "azurerm_virtual_network_peering" "virtualNetworkPeering" {
  name                         = var.peeringName
  resource_group_name          = var.resourceGroupName
  virtual_network_name         = var.sourceVirtualNetworkName
  remote_virtual_network_id    = var.remoteVirtualNetworkId
  allow_virtual_network_access = var.peeringOptions.allowVirtualNetworkAccess
  allow_forwarded_traffic      = var.peeringOptions.allowForwardedTraffic
  allow_gateway_transit        = var.peeringOptions.allowGatewayTransit
  use_remote_gateways          = var.peeringOptions.useRemoteGateways
}