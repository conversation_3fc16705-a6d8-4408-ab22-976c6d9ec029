resource "azurerm_kubernetes_cluster_node_pool" "NodeCC" {
  for_each              = var.nodePool
  name                  = each.value.name
  kubernetes_cluster_id = var.kubernetes_cluster_id
  vnet_subnet_id        = var.vnetId
  mode                  = each.value.mode
  vm_size               = each.value.vm_size
  os_type               = each.value.os_type
  os_sku                = each.value.os_sku
  os_disk_type          = each.value.os_disk_type
  os_disk_size_gb       = each.value.os_disk_size_gb
  auto_scaling_enabled  = each.value.enable_auto_scaling
  node_count            = each.value.node_count
  max_count   = each.value.max_count
  min_count   = each.value.min_count
  zones       = each.value.zones
  max_pods    = each.value.max_pods
  node_labels = each.value.node_labels
  lifecycle {
    ignore_changes = [
      max_count, min_count
    ]
  }
}