// REQUIRED VARIABLES (variables which are needed to be passed)
variable "keyVaultName" {
  description = "Name of the key vault"
  type        = string
}
variable "rgName" {
  description = "The name of the Azure Resource Group where the keyvault will be created"
  type        = string
}
variable "location" {
  description = "The location (region) where the Azure keyvault will be created"
  type        = string
}
variable "kvSkuName" {
  description = "The Name of the SKU used for this Key Vault"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "kvEnabledForDiskEncryption" {
  description = "Specify whether Azure Disk Encryption is permitted to retrieve secrets from the vault"
  type        = bool
}
variable "kvSoftDeleteRetentionDays" {
  description = "The number of days that items should be retained for once soft-deleted"
  type        = number
}
variable "kvPurgeProtectionEnabled" {
  description = "Defines whether Purge Protection enabled for this Key Vault"
  type        = bool
}
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "publicNetworkAccessEnabled" {
  description = "Defines whether public network access is allowed for this Key Vault"
  type        = bool
}
variable "enableRbacAuthorization" {
  description = "Defines whether to enable rbac authorization for key vault"
  type        = bool
}
variable "enabledForDeployment" {
  description = "Defines whether to enable for deployment"
  type        = bool
}
variable "enabledForTemplateDeployment" {
  description = "Defines whether to enable for template deployment"
  type        = bool
}
variable "kvIpRules" {
  description = "ip rules for key vault stores list of ip address to be whitelisted"
  type        = list(string)
}
variable "bypass" {
  description = "Bypass to which services"
  type        = string
}
variable "defaultAction" {
  description = "Default action for network acl of keyvault"
  type        = string
}