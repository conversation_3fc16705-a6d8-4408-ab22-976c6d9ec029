variable "kubernetes_cluster_id" {
  description = "The ID of the Kubernetes Cluster"
  type        = string
}
variable "vnetId" {
  description = "ID of a Subnet where the Kubernetes Node Pool should exist"
  type        = string
}
variable "nodePool" {
  description = "Defines the Node Pool Configuration which is present inside Kubernetes Cluster"
  type = map(object({
    name                = string
    mode                = string
    vm_size             = string
    os_type             = string
    os_sku              = string
    os_disk_type        = string
    os_disk_size_gb     = string
    enable_auto_scaling = bool
    node_count          = number
    max_count           = number
    min_count           = number
    zones               = list(string)
    max_pods            = number
    node_labels         = map(string)
  }))
}