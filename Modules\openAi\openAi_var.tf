// REQUIRED VARIABLES (variables which are needed to be passed)
variable "rgName" {
  description = "The name of the Azure resource group where the Open AI Account will be created"
  type        = string
}
variable "openAiName" {
  description = "The name of the Open AI Account"
  type        = string
}
variable "location" {
  description = "The Azure region where the Open AI Account will be deployed"
  type        = string
}
variable "openAiKind" {
  description = "The kind of the Open AI Account"
  type        = string
}
variable "openAiSkuName" {
  description = "The SKU (pricing tier) for the Open AI Account"
  type        = string
}
variable "oaiModel" {
  description = "The Details of Open Ai Model"
  type = map(object({
    oaDeploymentName   = string
    oaDeloymentSkuName = string
    modelFormat        = string
    modelName          = string
    modelVersion       = string
  }))
}


// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "oaCustomSubdomain" {
  description = "The subdomain name used for token-based authentication"
  type        = string
}
variable "publiceNetworkAccessEnabled" {
  description = "It defines whether public network access is allowed for the Cognitive Account"
  type        = bool
}
variable "identityType" {
  description = "The type of the Identity"
  type        = string
}
