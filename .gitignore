# Terraform .gitignore

# Local .terraform directories
**/.terraform/*

# .tfstate files
*.tfstate
*.tfstate.*

# Crash log files
crash.log
crash.*.log

# Exclude all .tfvars files, which are likely to contain sensitive data
# such as passwords, private keys, and other secrets. These should not be part of version control
# as they are data points which are potentially sensitive and subject to change depending on the environment.
*.tfvars
*.tfvars.json

# Ignore override files as they are usually used to override resources locally and so
# are not checked in
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Include override files you do wish to add to version control using negated pattern
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *tfplan*
*.tfplan
*.tfplan.*

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Ignore Mac .DS_Store files
.DS_Store

# Ignore Windows Thumbs.db files
Thumbs.db

# Ignore editor and IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Ignore log files
*.log

# Ignore backup files
*.backup
*.bak

# Ignore temporary files
*.tmp
*.temp

# Ignore lock files (uncomment if you want to ignore them)
.terraform.lock.hcl

# Ignore environment-specific files
.env
.env.local
.env.*.local

# Ignore Azure CLI files
.azure/

# Ignore AWS CLI files
.aws/

# Ignore GCP CLI files
.gcloud/

# Ignore Terraform Cloud/Enterprise files
.terraform.d/

# Ignore any local testing or development files
test/
tests/
*.test

# Ignore documentation build files
docs/_build/
site/

# Ignore package files
*.zip
*.tar.gz
*.tgz

# Ignore any local scripts that might contain sensitive information
local_*
*_local.*
