# Resource Groups Configuration
resource_groups = {
  "kartik.jindal_RG" = {
    name                = "kartik.jindal_RG"
    location            = "Central India"
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
    }
  }
}

# Virtual Networks Configuration
virtual_networks = {
  "hub_vnet" = {
    name                = "Hub-VNet"
    resource_group_name = "kartik.jindal_RG"
    location            = "Central India"
    address_space       = ["10.0.0.0/16"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Hub"
    }
  }
  "spoke_vnet" = {
    name                = "Spoke-VNet"
    resource_group_name = "kartik.jindal_RG"
    location            = "Central India"
    address_space       = ["********/16"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
    }
  }
}

# Subnets Configuration
subnets = {
  # Hub VNet Subnets
  "hub_gateway_subnet" = {
    name                                         = "Hub-Normal-Subnet"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "hub_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "hub_bastion_subnet" = {
    name                                         = "AzureBastionSubnet"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "hub_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "hub_firewall_subnet" = {
    name                                         = "AzureFirewallSubnet"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "hub_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  # Spoke VNet Subnets
  "spoke_subnet_1" = {
    name                                         = "Spoke-Subnet-1"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "spoke_subnet_2" = {
    name                                         = "Spoke-Subnet-2"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "spoke_subnet_3" = {
    name                                         = "Spoke-Subnet-3"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "spoke_subnet_4" = {
    name                                         = "Spoke-Subnet-4"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "spoke_subnet_5" = {
    name                                         = "Spoke-Subnet-5"
    resource_group_name                          = "kartik.jindal_RG"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
}

# Network Security Groups Configuration
network_security_groups = {
  "hub_nsg" = {
    name                = "Hub-NSG"
    location            = "Central India"
    resource_group_name = "kartik.jindal_RG"
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Hub"
    }
    security_rules = [
      {
        name                     = "Allow-HTTP"
        protocol                 = "Tcp"
        sourcePortRange          = "*"
        destinationPortRange     = "80"
        sourceAddressPrefix      = "*"
        destinationAddressPrefix = "*"
        access                   = "Allow"
        priority                 = 100
        direction                = "Inbound"
      },
      {
        name                     = "Allow-HTTPS"
        protocol                 = "Tcp"
        sourcePortRange          = "*"
        destinationPortRange     = "443"
        sourceAddressPrefix      = "*"
        destinationAddressPrefix = "*"
        access                   = "Allow"
        priority                 = 110
        direction                = "Inbound"
      }
    ]
  }
  "spoke_nsg" = {
    name                = "Spoke-NSG"
    location            = "Central India"
    resource_group_name = "kartik.jindal_RG"
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
    }
    security_rules = [
      {
        name                     = "Allow-Internal"
        protocol                 = "*"
        sourcePortRange          = "*"
        destinationPortRange     = "*"
        sourceAddressPrefix      = "10.0.0.0/8"
        destinationAddressPrefix = "10.0.0.0/8"
        access                   = "Allow"
        priority                 = 100
        direction                = "Inbound"
      }
    ]
  }
}

# Route Tables Configuration
route_tables = {
  "hub_route_table" = {
    name                         = "Hub-RouteTable"
    location                     = "Central India"
    resource_group_name          = "kartik.jindal_RG"
    disable_bgp_route_propagation = false
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Hub"
    }
    routes = [
      {
        name               = "ToSpoke"
        addressPrefix      = "********/16"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      }
    ]
  }
  "spoke_route_table_1" = {
    name                         = "Spoke-RouteTable-1"
    location                     = "Central India"
    resource_group_name          = "kartik.jindal_RG"
    disable_bgp_route_propagation = false
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
    }
    routes = [
      {
        name               = "ToHub"
        addressPrefix      = "10.0.0.0/16"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      },
      {
        name               = "Default"
        addressPrefix      = "0.0.0.0/0"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      }
    ]
  }
  "spoke_route_table_2" = {
    name                         = "Spoke-RouteTable-2"
    location                     = "Central India"
    resource_group_name          = "kartik.jindal_RG"
    disable_bgp_route_propagation = false
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
    }
    routes = [
      {
        name               = "ToHub"
        addressPrefix      = "10.0.0.0/16"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      }
    ]
  }
}

# NSG Associations Configuration
nsg_associations = {
  "hub_normal_subnet_nsg" = {
    nsg_key    = "hub_nsg"
    subnet_key = "hub_normal_subnet"
  }
  "spoke_subnet_1_nsg" = {
    nsg_key    = "spoke_nsg"
    subnet_key = "spoke_subnet_1"
  }
  "spoke_subnet_2_nsg" = {
    nsg_key    = "spoke_nsg"
    subnet_key = "spoke_subnet_2"
  }
  "spoke_subnet_3_nsg" = {
    nsg_key    = "spoke_nsg"
    subnet_key = "spoke_subnet_3"
  }
}

# Route Table Associations Configuration
route_table_associations = {
  "hub_normal_subnet_rt" = {
    route_table_key = "hub_route_table"
    subnet_key      = "hub_normal_subnet"
  }
  "spoke_subnet_1_rt" = {
    route_table_key = "spoke_route_table_1"
    subnet_key      = "spoke_subnet_1"
  }
  "spoke_subnet_2_rt" = {
    route_table_key = "spoke_route_table_1"
    subnet_key      = "spoke_subnet_2"
  }
  "spoke_subnet_4_rt" = {
    route_table_key = "spoke_route_table_2"
    subnet_key      = "spoke_subnet_4"
  }
  "spoke_subnet_5_rt" = {
    route_table_key = "spoke_route_table_2"
    subnet_key      = "spoke_subnet_5"
  }
}

# VNet Peering Configuration
vnet_peering = {
  "hub_to_spoke" = {
    name                = "hub-to-spoke-peering"
    resource_group_name = "kartik.jindal_RG"
    source_vnet_key     = "hub_vnet"
    remote_vnet_key     = "spoke_vnet"
    peering_options = {
      allowVirtualNetworkAccess = true
      allowForwardedTraffic     = true
      allowGatewayTransit       = false
      useRemoteGateways         = false
    }
  }
  "spoke_to_hub" = {
    name                = "spoke-to-hub-peering"
    resource_group_name = "kartik.jindal_RG"
    source_vnet_key     = "spoke_vnet"
    remote_vnet_key     = "hub_vnet"
    peering_options = {
      allowVirtualNetworkAccess = true
      allowForwardedTraffic     = true
      allowGatewayTransit       = false
      useRemoteGateways         = false
    }
  }
}

# Public IPs Configuration
public_ips = {
  "bastion_pip" = {
    name                = "Bastion-PublicIP"
    location            = "Central India"
    resource_group_name = "kartik.jindal_RG"
    allocation_method   = "Static"
    sku                 = "Standard"
    zones               = ["1"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Purpose     = "Bastion"
    }
  }
  "firewall_pip" = {
    name                = "Firewall-PublicIP"
    location            = "Central India"
    resource_group_name = "kartik.jindal_RG"
    allocation_method   = "Static"
    sku                 = "Standard"
    zones               = ["1"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Purpose     = "Firewall"
    }
  }
}