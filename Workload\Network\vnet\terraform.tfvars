# Resource Groups Configuration
resource_groups = {
  "hub_rg" = {
    name     = "kartik.jindal_RG"
    location = "Central India"
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Hub"
      Purpose     = "Hub Network Resources"
    }
  }
  "spoke_rg" = {
    name     = "kartik.jindal_RG"
    location = "Central India"
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
      Purpose     = "Spoke Network Resources"
    }
  }
}

# Virtual Networks Configuration
virtual_networks = {
  "hub_vnet" = {
    name          = "Hub-VNet"
    type          = "hub"
    location      = "Central India"
    address_space = ["10.0.0.0/16"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Hub"
    }
  }
  "spoke_vnet" = {
    name          = "Spoke-VNet"
    type          = "spoke"
    location      = "Central India"
    address_space = ["********/16"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
    }
  }
}

# Subnets Configuration
subnets = {
  # Hub VNet Subnets
  "hub_normal_subnet" = {
    name                                         = "Hub-Normal-Subnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "hub_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "hub_bastion_subnet" = {
    name                                         = "AzureBastionSubnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "hub_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "hub_firewall_subnet" = {
    name                                         = "AzureFirewallSubnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "hub_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  # Spoke VNet Subnets
  "ml_compute_subnet" = {
    name                                         = "ML-Compute-Subnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "aks_subnet" = {
    name                                         = "AKS-Subnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "compute_subnet" = {
    name                                         = "Compute-Subnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "vnet_integration_subnet" = {
    name                                         = "VNet-Integration-Subnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
  "private_endpoint_subnet" = {
    name                                         = "Private-Endpoint-Subnet"
    address_prefixes                             = ["********/24"]
    virtual_network_key                          = "spoke_vnet"
    private_link_service_network_policies_enabled = true
    delegations                                  = null
  }
}

# Network Security Groups Configuration
network_security_groups = {
  "hub_shared_services_nsg" = {
    name     = "Hub-Shared-Services-NSG"
    location = "Central India"
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Hub"
      Purpose     = "Shared Services"
    }
    security_rules = [
      {
        name                     = "Allow-HTTP"
        protocol                 = "Tcp"
        sourcePortRange          = "*"
        destinationPortRange     = "80"
        sourceAddressPrefix      = "*"
        destinationAddressPrefix = "*"
        access                   = "Allow"
        priority                 = 100
        direction                = "Inbound"
      },
      {
        name                     = "Allow-HTTPS"
        protocol                 = "Tcp"
        sourcePortRange          = "*"
        destinationPortRange     = "443"
        sourceAddressPrefix      = "*"
        destinationAddressPrefix = "*"
        access                   = "Allow"
        priority                 = 110
        direction                = "Inbound"
      }
    ]
  }
  "spoke_workload_nsg" = {
    name     = "Spoke-Workload-NSG"
    location = "Central India"
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
      Purpose     = "Workload Protection"
    }
    security_rules = [
      {
        name                     = "Allow-Internal"
        protocol                 = "*"
        sourcePortRange          = "*"
        destinationPortRange     = "*"
        sourceAddressPrefix      = "10.0.0.0/8"
        destinationAddressPrefix = "10.0.0.0/8"
        access                   = "Allow"
        priority                 = 100
        direction                = "Inbound"
      }
    ]
  }
}

# Route Tables Configuration
route_tables = {
  "hub_shared_services_rt" = {
    name                         = "Hub-Shared-Services-RouteTable"
    location                     = "Central India"
    disable_bgp_route_propagation = false
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Hub"
      Purpose     = "Shared Services Routing"
    }
    routes = [
      {
        name               = "ToSpoke"
        addressPrefix      = "********/16"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      }
    ]
  }
  "spoke_compute_workload_rt" = {
    name                         = "Spoke-Compute-Workload-RouteTable"
    location                     = "Central India"
    disable_bgp_route_propagation = false
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
      Purpose     = "Compute Workload Routing"
    }
    routes = [
      {
        name               = "ToHub"
        addressPrefix      = "10.0.0.0/16"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      },
      {
        name               = "Default"
        addressPrefix      = "0.0.0.0/0"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      }
    ]
  }
  "spoke_integration_rt" = {
    name                         = "Spoke-Integration-RouteTable"
    location                     = "Central India"
    disable_bgp_route_propagation = false
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Type        = "Spoke"
      Purpose     = "VNet Integration Routing"
    }
    routes = [
      {
        name               = "ToHub"
        addressPrefix      = "10.0.0.0/16"
        nextHopType        = "VirtualAppliance"
        nextHopInIpAddress = "********"
      }
    ]
  }
}

# NSG Associations Configuration
nsg_associations = {
  "hub_normal_subnet_nsg" = {
    nsg_key    = "hub_shared_services_nsg"
    subnet_key = "hub_normal_subnet"
  }
  "compute_subnet_nsg" = {
    nsg_key    = "spoke_workload_nsg"
    subnet_key = "compute_subnet"
  }
  "vnet_integration_subnet_nsg" = {
    nsg_key    = "spoke_workload_nsg"
    subnet_key = "vnet_integration_subnet"
  }
}

# Route Table Associations Configuration
route_table_associations = {
  "hub_normal_subnet_rt" = {
    route_table_key = "hub_shared_services_rt"
    subnet_key      = "hub_normal_subnet"
  }
  "compute_subnet_rt" = {
    route_table_key = "spoke_compute_workload_rt"
    subnet_key      = "compute_subnet"
  }
  "vnet_integration_subnet_rt" = {
    route_table_key = "spoke_integration_rt"
    subnet_key      = "vnet_integration_subnet"
  }
}

# VNet Peering Configuration
vnet_peering = {
  "hub_to_spoke" = {
    name            = "hub-to-spoke-peering"
    source_vnet_key = "hub_vnet"
    remote_vnet_key = "spoke_vnet"
    peering_options = {
      allowVirtualNetworkAccess = true
      allowForwardedTraffic     = true
      allowGatewayTransit       = false
      useRemoteGateways         = false
    }
  }
  "spoke_to_hub" = {
    name            = "spoke-to-hub-peering"
    source_vnet_key = "spoke_vnet"
    remote_vnet_key = "hub_vnet"
    peering_options = {
      allowVirtualNetworkAccess = true
      allowForwardedTraffic     = true
      allowGatewayTransit       = false
      useRemoteGateways         = false
    }
  }
}

# Public IPs Configuration
public_ips = {
  "bastion_pip" = {
    name              = "Bastion-PublicIP"
    location          = "Central India"
    allocation_method = "Static"
    sku               = "Standard"
    zones             = ["1"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Purpose     = "Bastion"
    }
  }
  "firewall_pip" = {
    name              = "Firewall-PublicIP"
    location          = "Central India"
    allocation_method = "Static"
    sku               = "Standard"
    zones             = ["1"]
    tags = {
      Environment = "Production"
      Department  = "Cloud Infra & Security"
      Purpose     = "Firewall"
    }
  }
}