{"version": 3, "terraform_version": "1.10.3", "backend": {"type": "azurerm", "config": {"access_key": "****************************************************************************************", "client_certificate_password": null, "client_certificate_path": null, "client_id": null, "client_secret": null, "container_name": "tfstate", "endpoint": null, "environment": null, "key": "terraform.tfstate", "metadata_host": null, "msi_endpoint": null, "oidc_request_token": null, "oidc_request_url": null, "oidc_token": null, "oidc_token_file_path": null, "resource_group_name": null, "sas_token": null, "snapshot": null, "storage_account_name": "tfprojstate", "subscription_id": "d833250a-cb43-419a-bf49-27b8942a3335", "tenant_id": null, "use_azuread_auth": null, "use_msi": null, "use_oidc": null}, "hash": **********}}