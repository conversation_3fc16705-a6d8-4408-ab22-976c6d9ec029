{"Modules": [{"Key": "", "Source": "", "Dir": "."}, {"Key": "network_security_groups", "Source": "../../../Modules/Network/networkSecurityGroup", "Dir": "../../../Modules/Network/networkSecurityGroup"}, {"Key": "nsg_associations", "Source": "../../../Modules/Network/Vnet/association/NSG", "Dir": "../../../Modules/Network/Vnet/association/NSG"}, {"Key": "public_ips", "Source": "../../../Modules/Network/publicIp", "Dir": "../../../Modules/Network/publicIp"}, {"Key": "resource_groups", "Source": "../../../Modules/resourceGroup", "Dir": "../../../Modules/resourceGroup"}, {"Key": "route_table_associations", "Source": "../../../Modules/Network/Vnet/association/RT", "Dir": "../../../Modules/Network/Vnet/association/RT"}, {"Key": "route_tables", "Source": "../../../Modules/Network/routeTable", "Dir": "../../../Modules/Network/routeTable"}, {"Key": "subnets", "Source": "../../../Modules/Network/Vnet/subnet", "Dir": "../../../Modules/Network/Vnet/subnet"}, {"Key": "virtual_networks", "Source": "../../../Modules/Network/Vnet/virtualNetwork", "Dir": "../../../Modules/Network/Vnet/virtualNetwork"}, {"Key": "vnet_peering", "Source": "../../../Modules/Network/Vnet/vnetPeering", "Dir": "../../../Modules/Network/Vnet/vnetPeering"}]}