resource "azurerm_machine_learning_workspace" "azureML" {
  name                          = var.azureMLSettings.workspaceName
  location                      = var.location
  resource_group_name           = var.rgName
  application_insights_id       = var.azureMLSettings.applicationInsightsId
  key_vault_id                  = var.azureMLSettings.keyVaultId
  storage_account_id            = var.azureMLSettings.storageAccountId
  container_registry_id         = var.azureMLSettings.containerRegistryId
  public_network_access_enabled = var.azureMLConfig.publicNetworkAccessEnabled
  image_build_compute_name      = var.azureMLConfig.imageBuildComputeName
  friendly_name                 = var.azureMLConfig.friendlyName
  high_business_impact          = var.azureMLConfig.highBusinessImpact
  description                   = var.azureMLConfig.description
  tags                          = var.tags

  identity {
    type         = var.identityType
    identity_ids = var.identityIds
  }

  dynamic "encryption" {
    for_each = var.encryption != null ? [var.encryption] : []
    content {
      key_vault_id = encryption.value.keyVaultId
      key_id       = encryption.value.keyId
    }
  }

  dynamic "primary_user_assigned_identity" {
    for_each = var.primaryUserAssignedIdentity != null ? [var.primaryUserAssignedIdentity] : []
    content {
      user_assigned_identity_id = primary_user_assigned_identity.value.userAssignedIdentityId
    }
  }
}