# Local values for resource group mappings
locals {
  # Resource group name mappings from variables
  hub_rg_name   = var.resource_groups["hub_rg"].name
  spoke_rg_name = var.resource_groups["spoke_rg"].name

  # Dynamic resource group assignment for virtual networks
  vnet_resource_groups = {
    for k, v in var.virtual_networks : k => v.type == "hub" ? local.hub_rg_name : local.spoke_rg_name
  }

  # Dynamic resource group assignment for subnets
  subnet_resource_groups = {
    for k, v in var.subnets : k => startswith(k, "hub_") ? local.hub_rg_name : local.spoke_rg_name
  }

  # Dynamic resource group assignment for NSGs
  nsg_resource_groups = {
    for k, v in var.network_security_groups : k => contains(["hub_shared_services_nsg"], k) ? local.hub_rg_name : local.spoke_rg_name
  }

  # Dynamic resource group assignment for route tables
  rt_resource_groups = {
    for k, v in var.route_tables : k => contains(["hub_shared_services_rt"], k) ? local.hub_rg_name : local.spoke_rg_name
  }

  # Dynamic resource group assignment for public IPs
  pip_resource_groups = {
    for k, v in var.public_ips : k => local.hub_rg_name  # All public IPs go to hub
  }

  # Dynamic resource group assignment for VNet peering
  peering_resource_groups = {
    for k, v in var.vnet_peering : k => contains(["hub_to_spoke"], k) ? local.hub_rg_name : local.spoke_rg_name
  }
}