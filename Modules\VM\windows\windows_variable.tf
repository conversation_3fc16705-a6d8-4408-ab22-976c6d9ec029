// REQUIRED VARIABLES (variables which are needed to be passed)
variable "vmName" {
  description = "Name of the Virtual Machine"
  type        = string
}
variable "rgName" {
  description = "Name of the Resource Group where the Virtual Machine will be created"
  type        = string
}
variable "location" {
  description = "Location (region) where the Virtual Machine will be created"
  type        = string
}
variable "nicName" {
  description = "Name of the network interface card"
  type        = string
}
variable "publicIpName" {
  description = "Name of the Public IP"
  type        = string
}
variable "subnetId" {
  description = "The ID of the Subnet where the Virtual Machine will be connected"
  type        = string
}
variable "computerName" {
  description = "Computer name for the Virtual Machine"
  type        = string
}
variable "osDiskName" {
  description = "Name of the OS Disk for the Virtual Machine"
  type        = string
}

variable "vmConfig" {
  description = "Configuration object for the Virtual Machine settings"
  type = object({
    vmSize                        = string # Size of the Virtual Machine (e.g., Standard_DS1_v2)
    adminUsername                 = string # Admin username for the Virtual Machine
    adminPassword                 = string # Admin password for the Virtual Machine
    caching                       = string # Caching mode for the OS Disk (e.g., ReadWrite, ReadOnly)
    storageAccountType            = string # Type of the storage account for the OS Disk (e.g., Standard_LRS, Premium_LRS)
    availabilityZone              = string # Availability Zone for the Virtual Machine
    publisher                     = string # Publisher of the Virtual Machine image
    offer                         = string # Offer of the Virtual Machine image
    sku                           = string # SKU of the Virtual Machine image
    version                       = string # Version of the Virtual Machine image SKU
    nicIpConfigurationName            = string # Name of the NIC IP configuration
    nicPrivateIpAddressAllocation = string # Private IP address allocation method for the NIC (e.g., Dynamic, Static)
    publicIpAllocationMethod        = string # Public IP allocation method for the Virtual Machine
  })
}


// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "publicIpSku" {
  description = "SKU of the Public IP to be used for the Virtual Machine"
  type        = string
  default     = "Standard"
}