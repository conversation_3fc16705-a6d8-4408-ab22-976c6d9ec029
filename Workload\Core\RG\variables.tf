# Azure Firewall Configuration
variable "azure_firewalls" {
  description = "Map of Azure Firewalls to create"
  type = map(object({
    name                    = string
    sku_name               = string
    sku_tier               = string
    zones                  = list(string)
    ip_configuration_name  = string
    tags                   = map(string)
  }))
  default = {}
}

# Azure Bastion Configuration
variable "azure_bastions" {
  description = "Map of Azure Bastion hosts to create"
  type = map(object({
    name = string
    bastion_settings = object({
      sku                 = string
      ip_configuration_name = string
      scale_units         = number
    })
    bastion_config = object({
      copy_paste_enabled = bool
      file_copy_enabled  = bool
      tunneling_enabled  = bool
    })
    tags = map(string)
  }))
  default = {}
}

# Application Gateway Configuration
variable "application_gateways" {
  description = "Map of Application Gateways to create"
  type = map(object({
    name                            = string
    sku                            = string
    tier                           = string
    capacity                       = number
    gateway_ip_configuration       = string
    frontend_port_name             = string
    port                           = number
    frontend_ip_configuration_name = string
    backend_address_pool_name      = string
    http_setting_name              = string
    cookie_based_affinity          = string
    path                           = string
    protocol                       = string
    request_timeout                = number
    listener_name                  = string
    request_routing_rule_name      = string
    rule_type                      = string
    priority                       = number
    tags                           = map(string)
  }))
  default = {}
}

# Private DNS Zones Configuration
variable "private_dns_zones" {
  description = "Map of Private DNS zones to create"
  type = map(object({
    name = string
    tags = map(string)
  }))
  default = {}
}

# Virtual Network Links Configuration
variable "vnet_links" {
  description = "Map of Virtual Network Links for Private DNS zones"
  type = map(object({
    name                 = string
    dns_zone_key        = string
    registration_enabled = bool
    tags                = map(string)
  }))
  default = {}
}