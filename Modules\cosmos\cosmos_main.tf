resource "azurerm_cosmosdb_account" "cosmosDb" {
  name                          = var.cosmosSettings.accountName
  location                      = var.location
  resource_group_name           = var.rgName
  offer_type                    = var.cosmosSettings.offerType
  kind                          = var.cosmosSettings.kind
  public_network_access_enabled = var.cosmosConfig.publicNetworkAccessEnabled
  analytical_storage_enabled    = var.cosmosConfig.analyticalStorageEnabled
  tags                          = var.tags

  consistency_policy {
    consistency_level       = var.consistencyPolicy.level
    max_interval_in_seconds = var.consistencyPolicy.maxIntervalInSeconds
    max_staleness_prefix    = var.consistencyPolicy.maxStalenessPrefix
  }

  dynamic "geo_location" {
    for_each = var.geoLocations
    content {
      location          = geo_location.value.location
      failover_priority = geo_location.value.failoverPriority
      zone_redundant    = geo_location.value.zoneRedundant
    }
  }

  dynamic "capabilities" {
    for_each = var.capabilities
    content {
      name = capabilities.value
    }
  }

  dynamic "backup" {
    for_each = var.backupPolicy == null ? [] : [var.backupPolicy]
    content {
      type                = backup.value.type
      interval_in_minutes = backup.value.intervalInMinutes
      retention_in_hours  = backup.value.retentionInHours
      storage_redundancy  = backup.value.storageRedundancy
    }
  }

  dynamic "virtual_network_rule" {
    for_each = var.virtualNetworkRules
    content {
      id                                   = virtual_network_rule.value.id
      ignore_missing_vnet_service_endpoint = virtual_network_rule.value.ignoreMissingVnetServiceEndpoint
    }
  }

  dynamic "cors_rule" {
    for_each = var.corsRules
    content {
      allowed_headers    = cors_rule.value.allowedHeaders
      allowed_methods    = cors_rule.value.allowedMethods
      allowed_origins    = cors_rule.value.allowedOrigins
      exposed_headers    = cors_rule.value.exposedHeaders
      max_age_in_seconds = cors_rule.value.maxAgeInSeconds
    }
  }

  lifecycle {
    ignore_changes = [
      capabilities,
    ]
  }
}

# Conditionally create SQL database if specified
resource "azurerm_cosmosdb_sql_database" "sqlDb" {
  for_each            = var.sqlDatabases
  name                = each.key
  resource_group_name = var.rgName
  account_name        = azurerm_cosmosdb_account.cosmosDb.name
  throughput          = lookup(each.value, "throughput", null)

  dynamic "autoscale_settings" {
    for_each = lookup(each.value, "maxThroughput", null) != null ? [1] : []
    content {
      max_throughput = each.value.maxThroughput
    }
  }
}
