// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "storageAccount" {
  source                     = "../" // Modifiy module path accordingly 
  storageAccountName         = "demostorageaccount"
  location                   = "Central India"
  rgName                     = "Demo-RG"
  accountTier                = "Standard"
  accountReplicationType     = "GRS"
  accountKind                = "BlockBlobStorage"
  enableHttpsTrafficOnly     = true
  minTlsVersion              = "TLS1_2"
  sharedAccessKeyEnabled     = true
  isHnsEnabled               = true
  publicNetworkAccessEnabled = true
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  networkRules = {
    defaultAction = "Deny"
  }
}