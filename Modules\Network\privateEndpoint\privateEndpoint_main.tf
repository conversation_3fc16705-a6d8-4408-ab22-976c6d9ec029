# Creates a private endpoint in Azure, associating it with a specified subnet within a resource group and location.
resource "azurerm_private_endpoint" "privateEndpoint" {
  name                = var.privateEndpointName
  location            = var.location
  resource_group_name = var.rgName
  subnet_id           = var.subnetEndpointId

  # Specifies the configuration for a private service connection, including its name, resource ID, subresource names, and whether it's a manual connection.
  private_service_connection {
    name                           = var.privateServiceConnectionName
    private_connection_resource_id = var.privateConnectionResourceId
    subresource_names              = var.privateConnectionSubresourceNames
    is_manual_connection           = var.isManualConnection
  }

  # Configures a group of private DNS zones, including the group name and associated zone IDs, if provided.
  dynamic "private_dns_zone_group" {
    for_each = var.privateDnsZoneGroupName == "" ? [] : [1]
    content {
      name                 = var.privateDnsZoneGroupName == "" ? null : var.privateDnsZoneGroupName
      private_dns_zone_ids = var.privateDnsZoneGroupName == [""] ? null : var.privateDnsZoneIds
    }
  }
}
