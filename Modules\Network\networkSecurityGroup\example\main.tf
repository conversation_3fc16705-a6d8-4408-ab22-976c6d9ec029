// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "nsg" {
  source   = "../" // Modifiy module path accordingly 
  nsgName  = "Demo-NSG"
  location = "Central India"
  rgName   = "Demo-RG"
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  secRule = [
    {
      name                     = "Demo-Rule"
      protocol                 = "Tcp"
      sourcePortRange          = "*"
      destinationPortRange     = "*"
      sourceAddressPrefix      = "*"
      destinationAddressPrefix = "*"
      access                   = "Allow"
      priority                 = 100
      direction                = "Inbound"
    }
  ]
}