// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "dataFactory" {
  source               = "../" // Modifiy module path accordingly 
  dataFactoryName      = "DemoDataFactory"
  location             = "Central India"
  rgName               = "Demo-RG"
  publicNetworkEnabled = true
  githubConfiguration = {
    github_config = {
      accountName       = "DemoAccount"
      branchName        = "main"
      gitUrl            = "" // Associate GitHub URL on this Place with the help of either Module (using Output), Locals or Resource Block
      repositoryName    = "DemoRepo"
      rootFolder        = "/adf"
      publishingEnabled = true
    }
  }
  globalParameter = {
    "Demo" = {
      name  = "Environment"
      value = "Production"
      type  = "String"
    }
  }
  identityType                 = "SystemAssigned"
  identityIds                  = [""]   // Associate Identity IDs on this Place with the help of either Module (using Output), Locals or Resource Block
  CustomerManagedKeyId         = "" // Associate Customer Managed Key ID on this Place with the help of either Module (using Output), Locals or Resource Block
  CustomerManagedKeyIdentityId = "" // Associate Customer Managed Key Identity ID on this Place with the help of either Module (using Output), Locals or Resource Block
}