// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "adfLinkedKv" {
  source                                      = "../" // Modifiy module path accordingly 
  linkedServiceKeyVaultName                   = "DemoKeyVaultLinkedService"
  linkedServiceKeyVaultDataFactoryId          = "" // Associate Data Factory ID on this Place with the help of either Module (using Output), Locals or Resource Block
  linkedServiceKeyVaultKeyVaultId             = "" // Associate Key Vault ID on this Place with the help of either Module (using Output), Locals or Resource Block
  linkedServiceKeyVaultDescription            = "This is a demo Key Vault linked service"
  linkedServiceKeyVaultIntegrationRuntimeName = "DemoRuntime"
}