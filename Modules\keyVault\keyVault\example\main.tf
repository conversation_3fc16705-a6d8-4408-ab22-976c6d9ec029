// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "keyVault" {
  source                       = "../" // Modifiy module path accordingly 
  keyVaultName                 = "Demo-KeyVault"
  location                     = "Central India"
  rgName                       = "Demo-RG"
  kvEnabledForDiskEncryption   = true
  kvSoftDeleteRetentionDays    = 30
  kvPurgeProtectionEnabled     = true
  kvSkuName                    = "standard"
  enabledForTemplateDeployment = true
  enabledForDeployment         = true
  enableRbacAuthorization      = true
  publicNetworkAccessEnabled   = true
  tags = {
    Environment = "Production"
    Department  = "Cloud Infra & Security"
  }
  defaultAction = "Deny"
  bypass        = "AzureServices"
  kvIpRules = ["0.0.0.0/0"]
}