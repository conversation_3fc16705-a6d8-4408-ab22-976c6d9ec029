resource "azurerm_search_service" "aiSearch" {
  name                          = var.aiSearchName
  resource_group_name           = var.aiSearchRgName
  location                      = var.aiSearchRgLocation
  public_network_access_enabled = var.publicNetworkAccessEnabled
  hosting_mode                  = var.aiServiceHostingMode
  partition_count               = var.aiPartitionCount
  replica_count                 = var.aiReplicaCount
  sku                           = var.aiSearchSku
  tags                          = var.aiSearchTag
}