// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "adfLinkedSftp" {
  source                                  = "../" // Modifiy module path accordingly 
  linkedServiceSftpName                   = "DemoSFTPLink"
  linkedServiceSftpDataFactoryId          = "" // Associate Data Factory ID on this Place with the help of either Module (using Output), Locals or Resource Block
  linkedServiceSftpDescription            = "This is a demo SFTP linked service"
  linkedServiceSftpSkipHostKeyValidation  = true
  linkedServiceSftpHostKeyFingerprint     = "SHA256:your_host_key_fingerprint"
  linkedServiceSftpAuthenticationType     = "Password"
  linkedServiceSftpHost                   = "http://www.bing.com"
  linkedServiceSftpPort                   = 22
  linkedServiceSftpUsername               = "UserAdmin"
  linkedServiceSftpPassword               = "India@1947"
  linkedServiceSftpIntegrationRuntimeName = "DemoSFTPRuntime"
}