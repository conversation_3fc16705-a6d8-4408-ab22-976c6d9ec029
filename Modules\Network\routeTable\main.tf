resource "azurerm_route_table" "routeTable" {
  name                          = var.rtName
  location                      = var.location
  resource_group_name           = var.rgName
  bgp_route_propagation_enabled = var.disableBgpRoutePropagation
  tags                          = var.tags
  dynamic "route" {
    for_each = var.rtRoutes
    content {
      name                   = route.value.name
      address_prefix         = route.value.addressPrefix
      next_hop_type          = route.value.nextHopType
      next_hop_in_ip_address = route.value.nextHopInIpAddress
    }
  }
}
