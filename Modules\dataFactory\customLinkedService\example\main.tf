// Hardcoded Values (can be modified with Variable Call using Locals or tfvars)

module "customLinkedService" {
  source                   = "../" // Modifiy module path accordingly 
  linkedServiceName        = "DemoLinkService"
  dataFactoryId            = "" // Associate Data Factory Id on this Place with the help of either Module (using Output), Locals or Resource Block
  linkedServiceType        = "AzureSqlDatabase"
  linkedServiceDescription = "Demo SQL Database Linked Service for Development"
  typePropertiesJson = jsonencode({
    connectionString = "Integrated Security=False;Encrypt=True;Connection Timeout=30;Data Source=demo-server.database.windows.net;Initial Catalog=demo-db"
  })
  parameters = {
    "DatabaseName" = "demoDatabase"
    "ServerName"   = "demoServer"
  }
  annotations = [
    "demo",
    "development"
  ]
  integrationRuntime = {
    "DemoRuntime" = {
      name = "demoAzureIr"
    }
  }
}