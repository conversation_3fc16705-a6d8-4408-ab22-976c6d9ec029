// REQUIRED VARIABLES (variables which are needed to be passed)
variable "rtName" {
  description = "The name of the Route Table"
  type        = string
}
variable "location" {
  description = "The location (region) where the Azure Route Table will be created"
  type        = string
}
variable "rgName" {
  description = "The name of the Azure Resource Group where the Route Table will be created"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "disableBgpRoutePropagation" {
  description = "Indicates whether BGP route propagation is disabled"
  type        = bool
}
variable "tags" {
  description = "A map of tags to assign to the resource"
  type        = map(string)
}
variable "rtRoutes" {
  description = "List of routes for the Azure Route Table"
  type = list(object({
    name               = string
    addressPrefix      = string
    nextHopType        = string
    nextHopInIpAddress = string
  }))
}
