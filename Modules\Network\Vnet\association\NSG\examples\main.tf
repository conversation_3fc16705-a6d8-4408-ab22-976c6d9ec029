module "nsgAssociation" {
  source                                   = "../"
  nsgId                                    = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/networkSecurityGroups/demo-nsg" // Associate Network Security Group Id on this Place with the help of either Module (using Output), Locals or Resource Block
  subnetId                                 = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/virtualNetworks/demo-vnet/subnets/demo-subnet" // Associate Subnet Id on this Place with the help of either Module (using Output), Locals or Resource Block
}