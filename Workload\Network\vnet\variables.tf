# Resource Groups Configuration
variable "resource_groups" {
  description = "Map of resource groups to create"
  type = map(object({
    name     = string
    location = string
    tags     = map(string)
  }))
  default = {}
}

# Virtual Networks Configuration
variable "virtual_networks" {
  description = "Map of virtual networks to create"
  type = map(object({
    name                = string
    resource_group_name = string
    location            = string
    address_space       = list(string)
    tags                = map(string)
  }))
  default = {}
}

# Subnets Configuration
variable "subnets" {
  description = "Map of subnets to create"
  type = map(object({
    name                                         = string
    resource_group_name                          = string
    address_prefixes                             = list(string)
    virtual_network_key                          = string
    private_link_service_network_policies_enabled = bool
    delegations = optional(object({
      subnetDelegationName  = string
      serviceDelegationName = string
      actions               = string
    }))
  }))
  default = {}
}

# Network Security Groups Configuration
variable "network_security_groups" {
  description = "Map of network security groups to create"
  type = map(object({
    name                = string
    location            = string
    resource_group_name = string
    tags                = map(string)
    security_rules = list(object({
      name                     = string
      priority                 = number
      direction                = string
      access                   = string
      protocol                 = string
      sourcePortRange          = string
      destinationPortRange     = string
      sourceAddressPrefix      = string
      destinationAddressPrefix = string
    }))
  }))
  default = {}
}

# Route Tables Configuration
variable "route_tables" {
  description = "Map of route tables to create"
  type = map(object({
    name                         = string
    location                     = string
    resource_group_name          = string
    disable_bgp_route_propagation = bool
    tags                         = map(string)
    routes = list(object({
      name               = string
      addressPrefix      = string
      nextHopType        = string
      nextHopInIpAddress = string
    }))
  }))
  default = {}
}

# NSG Associations Configuration
variable "nsg_associations" {
  description = "Map of NSG to subnet associations"
  type = map(object({
    nsg_key    = string
    subnet_key = string
  }))
  default = {}
}

# Route Table Associations Configuration
variable "route_table_associations" {
  description = "Map of route table to subnet associations"
  type = map(object({
    route_table_key = string
    subnet_key      = string
  }))
  default = {}
}

# VNet Peering Configuration
variable "vnet_peering" {
  description = "Map of VNet peering connections"
  type = map(object({
    name                = string
    resource_group_name = string
    source_vnet_key     = string
    remote_vnet_key     = string
    peering_options = object({
      allowVirtualNetworkAccess = bool
      allowForwardedTraffic     = bool
      allowGatewayTransit       = bool
      useRemoteGateways         = bool
    })
  }))
  default = {}
}

# Public IPs Configuration
variable "public_ips" {
  description = "Map of public IPs to create"
  type = map(object({
    name                = string
    location            = string
    resource_group_name = string
    allocation_method   = string
    sku                 = string
    zones               = list(string)
    tags                = map(string)
  }))
  default = {}
}
