// REQUIRED VARIABLES (variables which are needed to be passed)
variable "linkedServiceKeyVaultName" {
  description = "The name of the linked service to Key Vault"
  type        = string
}
variable "linkedServiceKeyVaultDataFactoryId" {
  description = "The ID of the data factory associated with the linked service to Key Vault"
  type        = string
}
variable "linkedServiceKeyVaultKeyVaultId" {
  description = "The ID of the Key Vault associated with the linked service"
  type        = string
}

// OPTIONAL VARIABLES (variables which are not necessary to be passed)
variable "linkedServiceKeyVaultDescription" {
  description = "The description of the linked service to Key Vault"
  type        = string
}
variable "linkedServiceKeyVaultIntegrationRuntimeName" {
  description = "The name of the integration runtime associated with the linked service to Key Vault"
  type        = string
}
