// Example usage of the Virtual Network Peering module with separate required variables and object-based options

module "virtualNetworkPeering" {
  source = "../" // Modify module path accordingly 
  
  // Required parameters as separate variables
  peeringName              = "vnet1-to-vnet2"
  resourceGroupName        = "Demo-RG"
  sourceVirtualNetworkName = "Demo-VNet1"
  remoteVirtualNetworkId   = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/virtualNetworks/Demo-VNet2"
  
  // Optional parameters as an object
  peeringOptions = {
    allowVirtualNetworkAccess = true
    allowForwardedTraffic     = false
    allowGatewayTransit       = false
    useRemoteGateways         = false
  }
}

// For bi-directional peering, create a second peering in the opposite direction
module "virtualNetworkPeeringReverse" {
  source = "../" // Modify module path accordingly 
  
  // Required parameters as separate variables
  peeringName              = "vnet2-to-vnet1"
  resourceGroupName        = "Demo-RG"
  sourceVirtualNetworkName = "Demo-VNet2"
  remoteVirtualNetworkId   = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/virtualNetworks/Demo-VNet1"
  
  // You can specify only the options you want to change from defaults
  peeringOptions = {
    allowForwardedTraffic = true
  }
}

// You can also omit the peeringOptions entirely to use all defaults
module "virtualNetworkPeeringMinimal" {
  source = "../" // Modify module path accordingly 
  
  peeringName              = "vnet3-to-vnet4"
  resourceGroupName        = "Demo-RG"
  sourceVirtualNetworkName = "Demo-VNet3"
  remoteVirtualNetworkId   = "/subscriptions/<subscription_id>/resourceGroups/Demo-RG/providers/Microsoft.Network/virtualNetworks/Demo-VNet4"
  
  // peeringOptions is optional and will use defaults if omitted
}